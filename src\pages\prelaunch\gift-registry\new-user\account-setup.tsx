/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowRight, Bank, CloseCircle, Moneys } from 'iconsax-react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { TextInput } from '../../../../components/inputs/text-input/text-input';
import { useQuery, useMutation } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';
import { ChevronDown } from 'lucide-react';

interface AccountDetailsProps {
  onNextStep: (data: { bank: string; accountNumber: string }) => void;
  initialData?: { bank?: string; accountNumber?: string };
}

interface Bank {
  bank_code: string;
  bank_name: string;
}

interface ValidationResult {
  account_name: string;
  account_number: string;
  bank_code: string;
}
type StatusMessage = {
  type: 'success' | 'error' | 'info' | 'validating' | null;
  content: string;
};

export const AccountSetup = ({
  onNextStep,
  initialData = {},
}: AccountDetailsProps) => {
  const [bank, setBank] = useState(initialData.bank || '');
  const [accountNumber, setAccountNumber] = useState(
    initialData.accountNumber || ''
  );
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [statusMessage, setStatusMessage] = useState<StatusMessage>({
    type: null,
    content: '',
  });
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {
    data: banks,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payoutBanks', 'ngn'],
    queryFn: () => GiftRegistryServices.getPayoutBanks('ngn'),
  });
  const validationMutation = useMutation({
    mutationFn: (payload: {
      bank_code: string;
      account_number: string;
      currency_code: string;
    }) => GiftRegistryServices.resolveUserPayoutBank(payload),
    onSuccess: (data) => {
      setValidationResult(data.data);
      setStatusMessage({
        type: 'success',
        content: data.data.account_name,
      });
    },
    onError: () => {
      setValidationResult(null);
      setStatusMessage({
        type: 'error',
        content: 'Invalid account details',
      });
    },
  });

  const createBankMutation = useMutation({
    mutationFn: (payload: {
      bank_code: string;
      account_number: string;
      currency_code: string;
    }) => GiftRegistryServices.createUserPayoutBank(payload),
    onSuccess: () => {
      onNextStep({
        bank,
        accountNumber,
      });
    },
    onError: (error: any) => {
      setValidationResult(null);
      setStatusMessage({
        type: 'error',
        content:
          error?.response?.data?.message ||
          'Failed to save bank details. Please try again.',
      });
    },
  });

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const validateAccount = useCallback(
    (bankCode: string, accNumber: string) => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }

      setValidationResult(null);
      setStatusMessage({
        type: 'validating',
        content: 'Validating account...',
      });
      validationTimeoutRef.current = setTimeout(() => {
        validationMutation.mutate({
          bank_code: bankCode,
          account_number: accNumber,
          currency_code: 'ngn',
        });
      }, 500);
    },
    [validationMutation.mutate]
  );

  useEffect(() => {
    if (bank && accountNumber && accountNumber.length >= 10) {
      validateAccount(bank, accountNumber);
    } else {
      setValidationResult(null);
      setStatusMessage({
        type: null,
        content: '',
      });
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    }

    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [bank, accountNumber, validateAccount]);

  const handleContinue = () => {
    if (validationResult) {
      setStatusMessage({ type: null, content: '' }); // Clear previous messages
      createBankMutation.mutate({
        bank_code: bank,
        account_number: accountNumber,
        currency_code: 'ngn',
      });
    }
  };

  // const renderStatusMessage = () => {
  //   // Show validation in progress
  //   if (validationMutation.isPending && bank && accountNumber) {
  //     return (
  //       <span className="text-xs italic text-grin-100">
  //         Validating account...
  //       </span>
  //     );
  //   }

  //   // Show validation success
  //   if (validationResult) {
  //     return (
  // <span className="text-sm flex items-center w-fit gap-2 rounded-full mt-2 font-bold italic text-grin-100 bg-grin py-1 px-2.5">
  //   <svg
  //     width="14"
  //     height="14"
  //     viewBox="0 0 14 14"
  //     fill="none"
  //     xmlns="http://www.w3.org/2000/svg">
  //     <rect x="1" y="1" width="12" height="12" rx="6" fill="#3CC35C" />
  //     <rect
  //       x="1"
  //       y="1"
  //       width="12"
  //       height="12"
  //       rx="6"
  //       stroke="#3CC35C"
  //       strokeWidth="2"
  //     />
  //     <path
  //       d="M9.18395 5.36328L6.18395 8.36328L4.82031 6.99964"
  //       stroke="white"
  //       strokeWidth="1.4"
  //       strokeLinecap="round"
  //       strokeLinejoin="round"
  //     />
  //   </svg>
  //   {validationResult.account_name}
  // </span>
  //     );
  //   }

  //   // Show validation error
  //   if (validationError) {
  //     return (
  //       <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4">
  //         <CloseCircle size={14} color="#B20000" variant="Bulk" />
  //         {validationError}
  //       </span>
  //     );
  //   }

  //   // Show save error (appears below other messages)
  //   if (saveError) {
  //     return (
  //       <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4 mt-2">
  //         <CloseCircle size={14} color="#B20000" variant="Bulk" />
  //         {saveError}
  //       </span>
  //     );
  //   }

  //   // Default info message
  //   if (!bank || !accountNumber) {
  //     return (
  //       <span className="text-xs italic text-grey-650">
  //         Enter account number to receive cash gift
  //       </span>
  //     );
  //   }

  //   return null;
  // };

  const isFormValid =
    bank &&
    accountNumber &&
    validationResult &&
    !validationMutation.isPending &&
    !createBankMutation.isPending;
  const renderStatusMessage = () => {
    if (!statusMessage.type) return null;

    switch (statusMessage.type) {
      case 'success':
        return (
          <span className="text-sm flex items-center w-fit gap-2 rounded-full mt-2 font-bold italic text-grin-100 bg-grin py-1 px-2.5">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <rect x="1" y="1" width="12" height="12" rx="6" fill="#3CC35C" />
              <rect
                x="1"
                y="1"
                width="12"
                height="12"
                rx="6"
                stroke="#3CC35C"
                strokeWidth="2"
              />
              <path
                d="M9.18395 5.36328L6.18395 8.36328L4.82031 6.99964"
                stroke="white"
                strokeWidth="1.4"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {statusMessage.content}
          </span>
        );
      case 'error':
        return (
          <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4">
            <CloseCircle size={14} color="#B20000" variant="Bulk" />
            {statusMessage.content}
          </span>
        );
      case 'validating':
        // Only show validating message if mutation is actually pending
        if (validationMutation.isPending) {
          return (
            <span className="text-xs italic text-grin-100">
              {statusMessage.content}
            </span>
          );
        }
        return null;
      case 'info':
        return (
          <span className="text-xs italic text-grey-650">
            {statusMessage.content}
          </span>
        );
      default:
        return null;
    }
  };
  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="md:text-[40px] text-2xl font-medium -tracking-[0.03em]">
          Add Account details
        </h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              type="file"
              accept="image/*"
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />
            <Moneys variant="Bulk" size="62" color="#FFAA8C" />
          </div>
          <div className="-mt-12">
            <div className="bg-cus-pink flex items-center gap-3 p-3.5 mb-6 rounded-2xl">
              <Bank color="#F7BFA9" size={56} variant="Bulk" />
              <p className="italic text-dark-blue-500 font-medium text-xs md:text-sm">
                Add your bank details so guests can easily contribute cash if
                <br /> they prefer to gift money instead of purchasing an item.
              </p>
            </div>
            {/* <div className="mb-6">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Bank
              </label>
              <select
                value={bank}
                onChange={(e) => setBank(e.target.value)}
                className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 italic outline-0"
                disabled={isLoading}>
                <option value="" disabled>
                  {isLoading ? 'Loading banks...' : 'Select a bank'}
                </option>
                {error ? (
                  <option value="" disabled>
                    Error loading banks
                  </option>
                ) : (
                  banks?.data?.map((bank: Bank) => (
                    <option key={bank?.bank_code} value={bank?.bank_code}>
                      {bank?.bank_name}
                    </option>
                  ))
                )}
              </select>
              {error && (
                <span className="text-xs italic text-red-500">
                  Failed to load banks. Please try again.
                </span>
              )}
            </div> */}
            <div className="mb-6 relative">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Bank
              </label>
              <div
                className="w-full h-[44px] px-4 py-2 bg-white border border-gray-300 rounded-full text-base font-bold text-grey-50 italic outline-0 flex items-center justify-between cursor-pointer"
                onClick={() => !isLoading && setIsOpen(!isOpen)}>
                {bank ? (
                  <span className="truncate">
                    {banks?.data?.find((b: any) => b.bank_code === bank)
                      ?.bank_name || 'Select a bank'}
                  </span>
                ) : (
                  <span className="text-grey-300">
                    {isLoading ? 'Loading banks...' : 'Select a bank'}
                  </span>
                )}
                <ChevronDown
                  size={20}
                  color="#717680"
                  className={`transition-transform ${
                    isOpen ? 'rotate-180' : ''
                  }`}
                />
              </div>

              {/* Custom dropdown menu */}
              {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white rounded-lg shadow-[0px_12px_120px_0px_#5F5F5F0F] max-h-60 overflow-y-auto [&::-webkit-scrollbar]:hidden">
                  {error ? (
                    <div className="px-4 py-3 text-sm text-red-600 border-b border-grey-100">
                      Failed to load banks. Please refresh your page.
                    </div>
                  ) : (
                    banks?.data?.map((bankItem: any) => (
                      <div
                        key={bankItem.bank_code}
                        onClick={() => {
                          setBank(bankItem.bank_code);
                          setIsOpen(false);
                        }}
                        className={`px-4 py-3 text-sm cursor-pointer border-b border-grey-150 last:border-b-0 transition-colors duration-150 ${
                          bank === bankItem.bank_code
                            ? 'text-primary-650 font-medium'
                            : 'text-grey-500'
                        }`}>
                        <span className="truncate">{bankItem.bank_name}</span>
                      </div>
                    ))
                  )}
                </div>
              )}

              {error && !isOpen && (
                <span className="text-xs italic text-red-500 mt-1 block">
                  Failed to load banks. Please try again.
                </span>
              )}
            </div>
            <div className="mb-6">
              <TextInput
                id="accountNumber"
                label="Account Number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                placeholder="e.g **********"
                className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
              />
              <div className="mt-3">{renderStatusMessage()}</div>
            </div>
            <button
              onClick={handleContinue}
              disabled={!isFormValid}
              className={`bg-primary-650 text-white py-2.5 px-4 mb-0 rounded-full cursor-pointer flex items-center gap-2 ${
                isFormValid ? '' : 'opacity-50 cursor-not-allowed'
              }`}>
              {isLoading ? 'Loading...' : 'Continue'}
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
