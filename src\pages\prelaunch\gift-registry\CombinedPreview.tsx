import { useState } from "react";
import { ArrowCircleRight2, <PERSON>Circle, Tag2 } from "iconsax-react";
import { Icon } from "../../../components/icons/icon";
import { Success } from "./success";
import stackMoney from "../../../assets/images/stack-money2.svg";
import {
  GiftRegistryServices,
  CreateCashGiftPayload,
  CreateItemsGiftPayload,
} from "../../../lib/services/gift-registry";
import { useEventStore } from "../../../lib/store/event";
import { AuthServices } from "../../../lib/services/auth";
import { events } from "../../../lib/services/events";
import { toast } from "react-toastify";

interface GiftItem {
  id: number;
  name: string;
  description?: string;
  price?: number | string;
  image: string | File;
  quantity?: number;
  link?: string;
  mostWanted?: boolean;
}

interface CashGift {
  id: number;
  amount: string;
  description: string;
  is_crowd_gift?: boolean;
}

interface GiftItemResponse {
  id: string;
  name: string;
  description: string;
  price: string;
  quantity: number;
  link: string;
  status: string;
  created_at: string;
  updated_at: string;
  event_id: string;
  currency_code: string;
  image_preview_url?: string;
}

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  giftItems?: GiftItem[];
  cashGifts?: CashGift[];
  bank?: string;
  accountNumber?: string;
  accountName?: string;
  location?: string;
}

interface CombinedPreviewProps {
  initialData?: RegistryData;
  onClose?: () => void;
}

export const CombinedPreview = ({
  initialData = {},
  onClose = () => {},
}: CombinedPreviewProps) => {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"gift-items" | "cash-gifts">(
    "gift-items"
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { selectedEvent } = useEventStore();

  // Debug logs
  console.log("CombinedPreview initialData:", initialData);
  console.log("Cash gifts:", initialData.cashGifts);
  console.log("Gift items:", initialData.giftItems);

  // Helper function to convert data URL to File
  const dataURLtoFile = (dataurl: string, filename: string): File => {
    const arr = dataurl.split(",");
    const mime = arr[0].match(/:(.*?);/)?.[1] || "image/jpeg";
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  // Helper function to get most wanted item
  const getMostWantedItem = () => {
    return (
      initialData.giftItems?.find((item) => item.mostWanted === true) || null
    );
  };

  // Helper function to upload gift item images
  const uploadGiftItemImages = async (
    createdItems: GiftItemResponse[],
    eventId: string
  ) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const createdItem of createdItems) {
      try {
        const originalItem = initialData.giftItems?.find(
          (item) => item.name === createdItem.name
        );

        if (!originalItem?.image) {
          warnings.push(`No image found for ${createdItem.name}`);
          continue;
        }

        let fileToUpload: File | null = null;

        if (originalItem.image instanceof File) {
          fileToUpload = originalItem.image;
        } else if (typeof originalItem.image === "string") {
          if (originalItem.image.startsWith("blob:")) {
            const response = await fetch(originalItem.image);
            const blob = await response.blob();
            fileToUpload = new File([blob], `gift-item-${createdItem.id}.jpg`, {
              type: blob.type || "image/jpeg",
            });
          } else if (originalItem.image.startsWith("data:")) {
            fileToUpload = dataURLtoFile(
              originalItem.image,
              `gift-item-${createdItem.id}.jpg`
            );
          } else {
            warnings.push(`Unknown image format for ${createdItem.name}`);
            continue;
          }
        }

        if (!fileToUpload) {
          warnings.push(`Failed to process image for ${createdItem.name}`);
          continue;
        }

        await AuthServices.uploadFiles(
          fileToUpload,
          "item_gift",
          eventId,
          createdItem.id
        );
      } catch (error) {
        console.error(`Image upload failed for ${createdItem.name}:`, error);
        errors.push(`Failed to upload image for ${createdItem.name}`);
      }
    }

    return { errors, warnings };
  };

  // Main function to handle combined gift registry creation
  const handleCreateGiftRegistry = async () => {
    const currentEventId = selectedEvent?.id;

    if (!currentEventId) {
      setError("No event selected. Please select an event first.");
      toast.error("No event selected. Please select an event first.");
      return;
    }

    const hasCashGifts =
      initialData.cashGifts && initialData.cashGifts.length > 0;
    const hasGiftItems =
      initialData.giftItems && initialData.giftItems.length > 0;

    if (!hasCashGifts && !hasGiftItems) {
      setError("No gifts to create. Please add some gifts first.");
      toast.error("No gifts to create. Please add some gifts first.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create cash gifts if they exist
      if (hasCashGifts) {
        const cashGiftPayload: CreateCashGiftPayload = {
          gifts: initialData.cashGifts!.map((gift) => ({
            amount: gift.amount.replace(/,/g, ""),
            description: gift.description,
            is_crowd_gift: gift.is_crowd_gift || false,
          })),
        };

        await GiftRegistryServices.createCashGift(
          currentEventId,
          cashGiftPayload
        );
        console.log("Cash gifts created successfully");
      }

      // Create gift items if they exist
      if (hasGiftItems) {
        const giftItemsPayload: CreateItemsGiftPayload = {
          gifts: initialData.giftItems!.map((item) => ({
            name: item.name,
            description: item.description || "",
            price:
              typeof item.price === "string"
                ? item.price.replace(/,/g, "")
                : String(item.price || 0),
            quantity: item.quantity || 1,
            link: item.link || "",
          })),
        };

        const response = await GiftRegistryServices.createItemsGift(
          currentEventId,
          giftItemsPayload
        );
        const createdItems: GiftItemResponse[] = response.data || [];

        if (createdItems.length === 0) {
          throw new Error("No gift items were created by the server");
        }

        // Upload images for gift items
        const uploadResult = await uploadGiftItemImages(
          createdItems,
          currentEventId
        );

        if (uploadResult.errors.length > 0) {
          uploadResult.errors.forEach((err) => toast.error(err));
        }

        if (uploadResult.warnings.length > 0) {
          console.warn("Image upload warnings:", uploadResult.warnings);
        }

        // Update most wanted gift if exists
        const mostWantedItem = getMostWantedItem();
        if (mostWantedItem && selectedEvent?.id) {
          try {
            const correspondingCreatedItem = createdItems.find(
              (createdItem) => createdItem.name === mostWantedItem.name
            );

            if (correspondingCreatedItem) {
              await events.updateEventDetails(selectedEvent.id, {
                most_wanted_gift_id: correspondingCreatedItem.id,
              });
            }
          } catch (mostWantedError) {
            console.error(
              "Failed to update most wanted gift ID:",
              mostWantedError
            );
          }
        }

        console.log("Gift items created successfully");
      }

      setOpen(true); // Show success modal
      toast.success("Gift registry created successfully!");
    } catch (err: unknown) {
      console.error("Error creating gift registry:", err);
      let errorMessage = "Failed to create gift registry. Please try again.";

      if (err && typeof err === "object") {
        const error = err as {
          response?: { data?: { message?: string } };
          message?: string;
        };
        errorMessage =
          error.response?.data?.message || error.message || errorMessage;
      }

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const accountDetails = {
    bank: initialData.bank || "GTBank",
    accountNumber: initialData.accountNumber || "**********",
    accountName: initialData.accountName || "ADE BOLUWATIFE",
    location:
      initialData.location || "Lekki Conservation Center, Lekki, Lagos State",
  };

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0">
        <div className="max-w-[560px] mx-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-[28px] font-semibold mt-5 italic mb-4">
              {initialData.registryTitle || "Oladele's birthday gifts"}
            </h1>
            <div className="flex justify-center  relative md:-right-[40px]">
              <button
                type="button"
                onClick={handleCreateGiftRegistry}
                disabled={isLoading}
                className="bg-primary text-base font-semibold cursor-pointer text-white rounded-full py-2.5 px-4 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "Creating..." : "Create Gift Registry"}
                <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
              </button>
            </div>
          </div>

          <div className="pt-5 px-4 pb-4 rounded-xl bg-[linear-gradient(182.72deg,#FEF7F4_20.31%,#F5F6FE_97.2%)]">
            <div className="text-2xl font-bold mb-1.5">
              {accountDetails.accountNumber}
            </div>
            <div>
              <span className="text-cus-orange-700 font-medium text-sm">
                {accountDetails.bank}{" "}
              </span>
              <span className="text-grey-950 text-sm italic font-bold">
                • {accountDetails.accountName}
              </span>
            </div>
            <div className="flex items-center text-sm italic text-dark-blue-200 gap-2 mt-4.5">
              <Icon name="bus" />
              <span>{accountDetails.location}</span>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Tab Navigation */}
          <div className="flex gap-1 mt-8 mb-4  p-1 rounded-full w-fit">
            <button
              onClick={() => setActiveTab("gift-items")}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                activeTab === "gift-items"
                  ? "bg-primary text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Gift Items
            </button>
            <button
              onClick={() => setActiveTab("cash-gifts")}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                activeTab === "cash-gifts"
                  ? "bg-primary text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Cash Gifts
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === "gift-items" && (
            <div>
              {initialData.giftItems && initialData.giftItems.length > 0 ? (
                <div className="space-y-4">
                  {initialData.giftItems.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center md:h-[180px] py-3 sm:py-0 flex-col md:flex-row border gap-4 border-grey-150 rounded-[14px] relative"
                    >
                      <img
                        src={
                          typeof item.image === "string"
                            ? item.image
                            : item.image instanceof File
                            ? URL.createObjectURL(item.image)
                            : ""
                        }
                        alt={item.name}
                        className="max-w-[155px] w-full h-full md:rounded-l-[14px]"
                      />
                      <div className="h-full w-full p-2">
                        <div className="flex justify-end ">
                          <button className="cursor-pointer hover:opacity-70 transition-opacity">
                            <CloseCircle
                              size={28}
                              variant="Bulk"
                              color="#9499F7"
                            />
                          </button>
                        </div>
                        <div>
                          <h2 className="text-[22px] text-grey-750 font-medium ">
                            {item.name}
                          </h2>
                          <p className="text-grey-100 my-1 max-w-[300px] truncate">
                            {item.description}
                          </p>
                        </div>
                        <div className="mt-4 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                          <Tag2 size={12} variant="Bulk" color="#5925DC " />
                          <span className="text-perple text-sm font-medium">
                            ₦
                            {item.price
                              ? typeof item.price === "string"
                                ? parseFloat(
                                    item.price.replace(/,/g, "")
                                  ).toLocaleString()
                                : Number(item.price).toLocaleString()
                              : "0"}
                          </span>
                        </div>
                        <div className="flex justify-end mb-3">
                          {item.mostWanted === true && (
                            <div className=" bg-primary-150 text-primary-750 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10 italic">
                              📍 MOST WANTED
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 px-4">
                  <div className="w-24 h-24 mb-4 opacity-50 bg-gray-200 rounded-lg flex items-center justify-center">
                    <Tag2 size={32} color="#9CA3AF" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-600 mb-2">
                    No Gift Items Added
                  </h3>
                  <p className="text-gray-500 text-center">
                    You haven't added any gift items yet. Go back to add some
                    items to your registry.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === "cash-gifts" && (
            <div>
              {initialData.cashGifts && initialData.cashGifts.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {initialData.cashGifts.map((cashGift) => (
                    <div
                      key={cashGift.id}
                      className="bg-[#F5F9FF] h-[259px] rounded-xl p-4 relative"
                    >
                      <button className="absolute top-2 right-2">
                        <CloseCircle color="#365B96" variant="Bulk" size={28} />
                      </button>
                      <img
                        src={stackMoney}
                        alt="Cash gift"
                        className="w-[104px] h-[88px] mt-4 mb-2"
                      />
                      <p className="text-[32px] font-bold">
                        ₦{Number(cashGift.amount).toLocaleString("en-NG")}
                      </p>
                      <p className="text-dark-blue-300 text-base italic mt-3">
                        {cashGift.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 px-4">
                  <img
                    src={stackMoney}
                    alt="No cash gifts"
                    className="w-24 h-24 mb-4 opacity-50"
                  />
                  <h3 className="text-xl font-medium text-gray-600 mb-2">
                    No Cash Gifts Added
                  </h3>
                  <p className="text-gray-500 text-center">
                    You haven't added any cash gifts yet. Go back to add some
                    cash gifts to your registry.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {open && (
        <Success
          onClose={onClose}
          itemCount={initialData.giftItems?.length || 0}
          cashCount={initialData.cashGifts?.length || 0}
        />
      )}
    </>
  );
};