import { motion, AnimatePresence } from 'framer-motion';
import celebrate from '../../assets/images/celebrate.png';
import { ArrowCircleRight2 } from 'iconsax-react';
interface TransactionPinSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TransactionPinSuccessModal: React.FC<
  TransactionPinSuccessModalProps
> = ({ isOpen, onClose }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-[999] flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 w-full max-w-[500px] relative rounded-[28px] bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            <div className="text-center">
              <div className="flex justify-center py-12 rounded-t-[28px] bg-[linear-gradient(180deg,_#FEF7F4_22.63%,_#FEF7F4_100%)]">
                <img
                  src={celebrate}
                  alt="celebrate"
                  className="h-[150px] w-[150px]"
                />
              </div>

              <h2 className="text-3xl font-semibold text-[#000026] mt-6">
                Your Transaction pin has
              </h2>
              <h3 className="text-3xl font-semibold text-[#808080] mb-4">
                been set successfully
              </h3>

              <p className="text-[#808080] text-base leading-relaxed max-w-sm mx-auto mb-8">
                You're all set. You can now safely withdraw your funds into your
                account
              </p>

              <button
                onClick={onClose}
                className="bg-primary flex gap-2 items-center mx-auto text-white px-8 py-3 rounded-full text-sm font-semibold hover:bg-primary-600 transition-colors mb-8">
                Continue
                <ArrowCircleRight2 size={20} color="#fff" variant="Bulk" />
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
