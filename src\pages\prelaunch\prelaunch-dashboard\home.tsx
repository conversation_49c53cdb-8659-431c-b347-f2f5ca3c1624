// import { Graph, Profile2User } from 'iconsax-react';
// import box from '../../../assets/images/box1.png';
// import AllBudgets from './all-budget';
import { SubHead } from '../../../components/dashboard/sub-head';
import { useEffect } from 'react';
import { useUserAuthStore } from '../../../lib/store/auth';
import { FirstTimer } from './first-timer';
import { useCompleteEventData } from '../../../lib/hooks/useCompleteEventData';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';

export const Home = () => {
  const { userData } = useUserAuthStore();
  const { selectedEvent } = useEventManagement();

  const userName = userData?.first_name;
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const {
    completeEventData,
    isLoading: isLoadingCompleteData,
    error: completeDataError,
  } = useCompleteEventData(selectedEvent?.id);
  if (completeDataError)
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        Sorry, An Error Occured, Kindly Refresh
      </div>
    );
  if (isLoadingCompleteData)
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  return (
    <div className="pt-32 ">
      <div className="mb-8 px-4 md:px-0">
        <h1 className="text-2xl font-bold text-gray-900 capitalize">
          Hi {userName},
        </h1>
        <p className="text-gray-600">Welcome Back, How can we help today?</p>
      </div>
      <SubHead completeEventData={completeEventData} />
      <FirstTimer completeEventData={completeEventData} />
      {/* <div className=" px-4 md:px-0">
        <div className="font-rethink flex flex-col md:flex-row justify-between">
          <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 md:max-w-[270px] w-full h-[320px] relative overflow-hidden">
            <div className="relative z-10  h-full ">
              <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
                GIFT REGISTRIES
              </h2>
              <span className="text-[64px] font-bold text-dark-blue-400">
                0
              </span>
            </div>
            <div className="absolute bottom-0 right-0 pointer-events-none ">
              <img src={box} alt="box" />
            </div>
          </div>
          <div className="md:max-w-[270px] w-full mt-5 md:mt-0">
            <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px]  h-[131px] p-4 ">
              <div className="flex justify-between items-center">
                <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
                  GUEST LISTS{' '}
                </h2>
                <div className="bg-primary-250 p-2.5 h-10 w-10 rounded-[64px]">
                  <Profile2User size="20" color="#82A7E2" variant="Bulk" />
                </div>
              </div>
              <span className="text-6xl font-bold text-dark-blue-400">0</span>
            </div>
            <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] h-[169px] mt-5 p-4  relative overflow-hidden">
              <div className="relative z-10  h-full ">
                <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
                  BUDGETS{' '}
                </h2>
                <span className="text-[64px] font-bold text-dark-blue-400">
                  0
                </span>
              </div>
              <div className="absolute top-[29px] left-[135px] pointer-events-none ">
                <Graph
                  color="#DBDDFC"
                  className="w-[180px] max-h-[180px]"
                  variant="Bulk"
                />
              </div>
            </div>
          </div>
        </div>
        <AllBudgets />
      </div> */}
    </div>
  );
};
