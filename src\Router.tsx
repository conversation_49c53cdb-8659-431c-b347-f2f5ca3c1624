import { LoginPage } from './pages/login/login';
import { SignupPage } from './pages/signup/signup';
import { createBrowserRouter } from 'react-router-dom';
import { NotFound } from './pages/notFound/notFound';
import {
  AuthRoute,
  ProtectedRoute,
  OnboardingRoute,
  PublicRoute,
} from './lib/protectedRoutes';
import { OAuthCallback } from './pages/signup/oauth-callback';
import { PrelaunchDashboard } from './pages/prelaunch/prelaunch-dashboard/dashboard';
import { Home } from './pages/prelaunch/prelaunch-dashboard/home';
import { ForgotPassword } from './pages/forgotPassword/forgot-password';
import { Settings } from './pages/settings/setting';
import { OnboardingQuestions } from './pages/onboarding/onboarding-questions';
import { Registering } from './pages/prelaunch/register-as-guest/registering';
import { GuestList } from './pages/prelaunch/guestlist/guestlist';
// import { GiftRegistry } from "./pages/prelaunch/gift-registry/gift-registry/gift-registry";
import { GiftRegistryDetails } from './pages/prelaunch/gift-registry/gift-registry-details/gift-registry-details';
import { GiftItemDetails } from './pages/prelaunch/gift-registry/gift-item-details/gift-item-details';
import { CashGiftDetails } from './pages/prelaunch/gift-registry/cash-gift-details/cash-gift-details';
import { CashGiftReservation } from './pages/prelaunch/gift-registry/purchasing-gifts/cash-gift-reservation';
import {
  SelectAccount,
  WithdrawalAmount,
  AddBankDetails,
  Authenticate,
} from './pages/prelaunch/gift-registry/withdrawal-flow';
import { BackFromJumia } from './pages/prelaunch/gift-registry/purchasing-gifts/back-from-jumia';
import { ViewingGiftAsGuest } from './pages/prelaunch/gift-registry/gift-item-as-guest/viewing-gift-as-guest';
import { GuestPortal } from './pages/GuestPortal/GuestPortal';
import { Wallet } from './pages/prelaunch/prelaunch-dashboard';
import { ItemsWithCash } from './pages/prelaunch/gift-registry/purchasing-gifts/items-with-cash';
import { EditEvents } from './pages/prelaunch/event-details/edit-events';
import { DeleteEventSuccess } from './pages/prelaunch/event-details/delete-event-success';
import { SetGiftRegistry } from './pages/prelaunch/gift-registry/new-user/set-gift-registry';
import { EventSelection } from './pages/event-selection/event-selection';

export const router = createBrowserRouter([
  {
    element: <AuthRoute />,
    children: [
      {
        path: '/login',
        element: <LoginPage />,
      },

      {
        path: '/signup',
        element: <SignupPage />,
      },
      {
        path: '/guest-registry',
        element: <Registering />,
      },
      {
        path: '/forgot-password',
        element: <ForgotPassword />,
      },

      {
        path: '/oauth',
        element: <OAuthCallback />,
      },
    ],
  },

  {
    path: '/onboarding',
    children: [
      {
        index: true,
        element: (
          <OnboardingRoute>
            <OnboardingQuestions />
          </OnboardingRoute>
        ),
      },
    ],
  },

  {
    path: '/select-event',
    element: (
      <ProtectedRoute>
        <EventSelection />
      </ProtectedRoute>
    ),
  },

  {
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
    element: <PrelaunchDashboard />,
    children: [
      {
        path: '/',
        children: [
          {
            index: true,
            element: <Home />,
          },
        ],
      },
      {
        path: '/guest-lists',
        element: <GuestList />,
      },
      {
        path: '/gift-registry-onboarding',
        element: <SetGiftRegistry />,
      },
      {
        path: '/gift-registry',
        element: <GiftRegistryDetails />,
        // element: <GiftRegistry />,
      },
      {
        path: '/budget-planner',
        element: <div className="py-68">BUDGET PLANNER</div>,
      },
      {
        path: '/wallet',
        element: (
          <ProtectedRoute>
            <Wallet />
          </ProtectedRoute>
        ),
      },
    ],
  },
  // {
  //   path: '/gift-registry/:id',
  //   element: (
  //     <ProtectedRoute>
  //       <GiftRegistryDetails />
  //     </ProtectedRoute>
  //   ),
  // },
  {
    path: '/edit-event-details',
    element: (
      <ProtectedRoute>
        <EditEvents />
      </ProtectedRoute>
    ),
  },
  {
    path: '/delete-event-success',
    element: (
      <ProtectedRoute>
        <DeleteEventSuccess />
      </ProtectedRoute>
    ),
  },
  {
    path: '/gift-registry/:registryId/gift/:giftId',
    element: (
      <ProtectedRoute>
        <GiftItemDetails />{' '}
      </ProtectedRoute>
    ),
  },
  {
    path: '/back-from-jumia',
    element: <BackFromJumia />,
  },
  {
    path: '/purchasing-gift-items',
    element: <ItemsWithCash />,
  },

  {
    path: '/gift-registry/:registryId/gift/:giftId',
    element: (
      <ProtectedRoute>
        <GiftItemDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/back-from-jumia',
    element: <BackFromJumia />,
  },
  {
    path: '/purchasing-gift-items',
    element: <ItemsWithCash />,
  },

  {
    path: '/gift-registry/:registryId/cash/:cashId',
    element: (
      <ProtectedRoute>
        <CashGiftDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/gift-registry/:registryId/cash/:cashId/reserve/:crowdfunding?',
    element: (
      <ProtectedRoute>
        <CashGiftReservation />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/select-account',
    element: (
      <ProtectedRoute>
        <SelectAccount />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/amount',
    element: (
      <ProtectedRoute>
        <WithdrawalAmount />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/add-bank-details',
    element: (
      <ProtectedRoute>
        <AddBankDetails />
      </ProtectedRoute>
    ),
  },
  {
    path: '/withdrawal/authenticate',
    element: (
      <ProtectedRoute>
        <Authenticate />
      </ProtectedRoute>
    ),
  },
  {
    path: '/settings',
    element: (
      <ProtectedRoute>
        <Settings />
      </ProtectedRoute>
    ),
  },
  // Guest Portal Routes - Public access for invited guests
  {
    path: '/guest/invite',

    element: (
      <PublicRoute>
        <GuestPortal />
      </PublicRoute>
    ),
  },

  // Guest Gift Routes - Public access for viewing gifts
  {
    path: '/guest/events/:eventId/gifts',
    element: <ViewingGiftAsGuest />,
  },
  {
    path: '/guest/events/:eventId/gifts/cash/:giftId',
    element: <ViewingGiftAsGuest />,
  },
  {
    path: '/guest/events/:eventId/gifts/item/:giftId',
    element: <ViewingGiftAsGuest />,
  },
  {
    path: '*',
    element: <NotFound />,
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
  },
]);

{
  /* Modal for Delivery Details */
}
