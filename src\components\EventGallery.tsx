import { useState } from "react";

interface EventImage {
  id: string;
  preview_url: string;
  preview_key: string;
}

interface EventGalleryProps {
  onAccept: () => void;
  eventImages?: EventImage[];
  invitationStatus?: "pending" | "accepted" | "rejected";
}

const fallbackImages = [
  "/gallery/gallery-1.png",
  "/gallery/gallery-2.png",
  "/gallery/gallery-3.png",
  "/gallery/gallery-4.png",
  "/gallery/gallery-5.png",
];

export const EventGalleryOverlay = ({ onAccept }: { onAccept: () => void }) => (
  <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-[8px]">
    <button
      className="bg-[#4d55f2] hover:bg-[#343cd8] text-white font-semibold rounded-full px-12 py-5 text-xl shadow-lg transition focus:outline-none focus:ring-2 focus:ring-[#4d55f2] focus:ring-offset-2"
      onClick={onAccept}
    >
      Accept to view
    </button>
  </div>
);

export const EventGallery = ({
  onAccept,
  eventImages,
  invitationStatus = "pending",
}: EventGalleryProps) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [activeImg, setActiveImg] = useState<string | null>(null);

  // Use event images if available, otherwise fallback to default images
  const galleryImages =
    eventImages && eventImages.length > 0
      ? eventImages.map((img) => img.preview_url)
      : fallbackImages;

  return (
    <div className="ml-[72px] relative flex justify-center w-[864px] xl:w-[1068px] 2xl:w-[1200px] items-center min-h-[400px] bg-gradient-to-br from-[#FEF7F4] to-[#F5F6FE] py-10 px-2 md:px-0">
      <div className="w-full  flex flex-col gap-6">
        {/* Gallery Grid */}
        <div className="grid grid-cols-3 grid-rows-2 gap-[26px]">
          {galleryImages.map((src, i) => (
            <div
              key={src}
              className="rounded-[14px] overflow-hidden cursor-pointer shadow-lg bg-white h-[276px] flex items-center justify-center"
              onClick={() => {
                setActiveImg(src);
                setModalOpen(true);
              }}
            >
              <img
                src={src}
                alt={`Gallery ${i + 1}`}
                className="object-cover w-full h-full transition-transform duration-200 hover:scale-105"
              />
            </div>
          ))}
          {/* If only 5 images, add an empty cell for symmetry */}
          {galleryImages.length === 5 && <div className="" />}
        </div>

        {/* Modal Popup */}
        {modalOpen && activeImg && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm">
            <div className="relative bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[90vh] flex items-center justify-center">
              <button
                className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md z-10"
                onClick={() => setModalOpen(false)}
                aria-label="Close"
              >
                <img
                  src="/icons/minimize.svg"
                  alt="minimize"
                  className="w-7 h-7"
                />
              </button>
              <img
                src={activeImg}
                alt="Gallery Large"
                className="object-cover w-full h-[600px] max-h-[80vh] max-w-full rounded-xl"
              />
            </div>
          </div>
        )}
      </div>
      {/* Only show overlay if invitation is still pending */}
      {invitationStatus === "pending" && (
        <EventGalleryOverlay
          onAccept={() => {
            onAccept();
            setModalOpen(false);
          }}
        />
      )}
    </div>
  );
};
