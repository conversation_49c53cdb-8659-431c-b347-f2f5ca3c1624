import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Header } from '../../../components/dashboard/header';
import { Footer } from '../footer';
import { useEffect } from 'react';
import { isTokenValid } from '../../../lib/helpers';
import { useUserAuthStore } from '../../../lib/store/auth';
import { useEventStore } from '../../../lib/store/event';

export const PrelaunchDashboard = () => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents, selectedEvent } = useEventStore();
  const { clearAllEventData } = useEventStore();

  const location = useLocation();

  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        clearAllEventData();

        console.error('Session expired, please login again');
      }
      navigate('/login');
      return;
    }
    if (userEvents.length === 0) {
      navigate('/onboarding');
      return;
    }
    // If user has events but no event is selected, redirect to event selection
    if (userEvents.length > 0 && !selectedEvent) {
      navigate('/select-event');
      return;
    }
  }, [
    token,
    navigate,
    clearAuthData,
    clearAllEventData,
    userEvents,
    selectedEvent,
  ]);
  if (
    !token ||
    (token && !isTokenValid(token)) ||
    userEvents.length === 0 ||
    !selectedEvent
  ) {
    return null;
  }

  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div
        className={` mx-auto ${
          location.pathname.includes('wallet') ? 'w-full' : 'max-w-[561px]'
        }`}>
        <Header />
        <Outlet />
      </div>
      <Footer />
    </div>
  );
};
