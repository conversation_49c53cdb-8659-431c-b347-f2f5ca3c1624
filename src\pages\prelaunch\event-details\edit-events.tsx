import { useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect } from 'react';
import { RefreshCw, X } from 'lucide-react';
import { ArrowLeft, Calendar, Clock } from 'iconsax-react';
import { formatDate, formattingTime } from '../../../lib/helpers';
import { useEventStore } from '../../../lib/store/event';
import { format } from 'date-fns';
import { useMutation } from '@tanstack/react-query';
import { events, UpdateEventPayload } from '../../../lib/services/events';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { toast } from 'react-toastify';
import {
  EditEventModal,
  SwitchEventPreferenceModal,
  EventSavedSuccessModal,

} from '../../../components/modals';
import { EventDatePicker } from '../../../components/date-picker';
import { EditEventFormData } from '../../../types/editEvent';
import { DateRange } from 'react-day-picker';

export const EditEvents = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();

  // Modal state
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSwitchModalOpen, setIsSwitchModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

  const [pendingPreference, setPendingPreference] = useState<string>('');

  //   // Helper function to map visibility to preference
  //   const getPreferenceFromVisibility = (visibility?: string) => {
  //     switch (visibility?.toLowerCase()) {
  //       case 'private':
  //         return 'Private Event';
  //       case 'invite_only':
  //         return 'Invite Only';
  //       case 'public':
  //       case 'open':
  //       default:
  //         return 'Open Event';
  //     }
  //   };

  // Form state
  const [formData, setFormData] = useState<EditEventFormData>({
    eventName: selectedEvent?.title || '',
    eventDescription: selectedEvent?.description || '',
    eventDate: selectedEvent?.date_from || '',
    eventTime: selectedEvent?.date_from
      ? formattingTime(selectedEvent.date_from)
      : '',
    // For testing: temporarily set to Private Event to test the modal
    // preference: getPreferenceFromVisibility(
    //   (selectedEvent as CompleteEventData)?.visibility || 'public'
    // ),
    preference: 'Private Event', // Temporary for testing
    location: selectedEvent?.location_address || '',
  });

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    selectedEvent?.date_from
      ? {
          from: new Date(selectedEvent.date_from),
          to: selectedEvent?.date_to
            ? new Date(selectedEvent.date_to)
            : undefined,
        }
      : undefined
  );

  // Time picker state
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);

  const [images, setImages] = useState([
    {
      id: 1,
      url: 'https://images.unsplash.com/photo-1464047736614-af63643285bf?w=800&h=600&fit=crop',
      alt: 'Event image 1',
    },
    {
      id: 2,
      url: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800&h=600&fit=crop',
      alt: 'Event image 2',
    },
    {
      id: 3,
      url: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop',
      alt: 'Event image 3',
    },
  ]);

  const [hoveredImage, setHoveredImage] = useState<number | null>(null);

  // Time options for dropdown
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });

  // Update event mutation
  const updateEventMutation = useMutation({
    mutationFn: (payload: UpdateEventPayload) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return events.updateEventDetails(selectedEvent.id, payload);
    },
    onSuccess: (response) => {
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);
      setIsEditModalOpen(false);
      toast.success('Event updated successfully!');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update event';
      toast.error(errorMessage);
    },
  });

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Handle date range selection
  const handleDateRangeSelect = (range: DateRange | undefined) => {
    if (range) {
      setDateRange(range);
      if (range.from) {
        const dateStr = format(range.from, 'yyyy-MM-dd');
        handleInputChange('eventDate', dateStr);
      }
    }
  };

  // Handle time selection
  const handleTimeSelect = (time: string) => {
    handleInputChange('eventTime', time);
    setShowTimePicker(false);
  };

  // Handle preference change request (shows confirmation modal)
  const handlePreferenceChangeRequest = (newPreference: string) => {
    setPendingPreference(newPreference);
    setIsSwitchModalOpen(true);
  };

  // Handle confirmed preference switch
  const handleConfirmPreferenceSwitch = () => {
    handleInputChange('preference', pendingPreference);
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };

  // Handle cancelled preference switch
  const handleCancelPreferenceSwitch = () => {
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };

  // Helper function to map preference to visibility
  // const getVisibilityFromPreference = (preference: string) => {
  //   switch (preference) {
  //     case 'Private Event':
  //       return 'private';
  //     case 'Invite Only':
  //       return 'invite_only';
  //     case 'Open Event':
  //     default:
  //       return 'public';
  //   }
  // };

  // Handle save success (show success modal)
  const handleSaveSuccess = () => {
    setIsEditModalOpen(false);
    setIsSuccessModalOpen(true);
  };

  // Handle form submission
  // const handleSaveChanges = () => {
  //   if (!selectedEvent?.id) return;

  //   // Comment out API call for now - just show success modal
  //   // const payload: UpdateEventPayload = {
  //   //   title: formData.eventName,
  //   //   description: formData.eventDescription,
  //   //   date_from: dateRange?.from
  //   //     ? format(dateRange.from, "yyyy-MM-dd'T'HH:mm:ss")
  //   //     : undefined,
  //   //   date_to: dateRange?.to
  //   //     ? format(dateRange.to, "yyyy-MM-dd'T'HH:mm:ss")
  //   //     : undefined,
  //   //   visibility: getVisibilityFromPreference(formData.preference),
  //   // };
  //   // updateEventMutation.mutate(payload);

  //   // For now, just show success modal after a short delay
  //   setTimeout(() => {
  //     handleSaveSuccess();
  //   }, 500);
  // };


  // Handle click outside for time picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        timePickerRef.current &&
        !timePickerRef.current.contains(event.target as Node)
      ) {
        setShowTimePicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false);
        } else if (showTimePicker) {
          setShowTimePicker(false);
        } else if (isEditModalOpen) {
          setIsEditModalOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDatePicker, showTimePicker, isEditModalOpen]);

  const changeImage = (imageId: number) => {
    const newImageUrl = `https://images.unsplash.com/photo-${Math.floor(
      Math.random() * 1000000000
    )}?w=800&h=600&fit=crop`;
    setImages(
      images.map((img) =>
        img.id === imageId ? { ...img, url: newImageUrl } : img
      )
    );
  };

  const deleteImage = (imageId: number) => {
    setImages(images.filter((img) => img.id !== imageId));
  };

  const eventDate = formatDate(selectedEvent?.date_from);
  const eventTime = formattingTime(selectedEvent?.date_from);

  // Format date range text for display
  const dateRangeText =
    dateRange?.from && dateRange?.to
      ? `${format(dateRange.from, 'MMM dd')} - ${format(
          dateRange.to,
          'MMM dd, yyyy'
        )}`
      : dateRange?.from
      ? format(dateRange.from, 'MMM dd, yyyy')
      : 'Select Date Range';
  return (
    <div className="bg-[linear-gradient(229.47deg,_#FEFAF8_38.81%,_#F5F6FE_851.11%)]">
      <div className=" min-h-screen max-w-[1160px] mx-auto">
        {/* Header */}
        <div className="flex items-center gap-3 py-8">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className={` bg-white rounded-full cursor-pointer `}>
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>{' '}
            </div>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Event Details</h1>
        </div>

        {/* Content */}
        <div className="">
          {/* Event Header */}
          <div className="flex items-center space-x-4 mb-8 bg-white py-9 px-10">
            <div className="w-[130px] h-[130px] rounded-xl overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop"
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h2 className="text-[36px] font-medium">Tolani's Birthday</h2>
                <button
                  onClick={() => setIsEditModalOpen(true)}
                  className="text-dark-blue-100 text-sm bg-primary-250 px-3.5 py-2 rounded-full font-semibold">
                  Edit Details{' '}
                </button>
              </div>
              <p className="text-grey-250 text-base">
                My 18th birthday celebration
              </p>
              <div className=" flex gap-4 my-4">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              <div className=" mt-2 uppercase text-grey-950 text-xs italic">
                📌{' '}
                <span>
                  Eko Hotels, 1415 Adetokunbo Ademola Street, Victoria Island,
                  Lagos 106104, Lagos
                </span>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* Large Image */}
            <div
              className="relative group cursor-pointer rounded-xl overflow-hidden"
              onMouseEnter={() => setHoveredImage(images[0]?.id)}
              onMouseLeave={() => setHoveredImage(null)}>
              {images[0] && (
                <>
                  <img
                    src={images[0].url}
                    alt={images[0].alt}
                    className="w-[567px] h-[460px] object-cover"
                  />
                  {hoveredImage === images[0].id && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center space-x-3">
                      <button
                        onClick={() => changeImage(images[0].id)}
                        className="px-4 py-2 bg-white text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 flex items-center space-x-2">
                        <RefreshCw size={14} />
                        <span>Change Image</span>
                      </button>
                      <button
                        onClick={() => deleteImage(images[0].id)}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 flex items-center space-x-2">
                        <X size={14} />
                        <span>Delete Image</span>
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Event Modal */}
      <EditEventModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        formData={formData}
        onInputChange={handleInputChange}
        isLoading={updateEventMutation.isPending}
        dateRangeText={dateRangeText}
        onDatePickerOpen={() => setShowDatePicker(true)}
        showTimePicker={showTimePicker}
        onTimePickerToggle={() => setShowTimePicker(true)}
        timeOptions={timeOptions}
        onTimeSelect={handleTimeSelect}
        timePickerRef={timePickerRef}
        onPreferenceChangeRequest={handlePreferenceChangeRequest}
        onSaveSuccess={handleSaveSuccess}

      />

      {/* Switch Event Preference Modal */}
      <SwitchEventPreferenceModal
        isOpen={isSwitchModalOpen}
        onClose={handleCancelPreferenceSwitch}
        onConfirm={handleConfirmPreferenceSwitch}
      />

      {/* Event Saved Success Modal */}
      <EventSavedSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
      />


      {/* Date Picker Modal */}
      <EventDatePicker
        isOpen={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        dateRange={dateRange}
        onDateRangeSelect={handleDateRangeSelect}
      />
    </div>
  );
};
