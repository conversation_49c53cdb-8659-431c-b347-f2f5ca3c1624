/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  // Add,
  ArrowDown2,
} from 'iconsax-react';
import box from '../../../assets/images/box1.png';
import guestcard from '../../../assets/images/guestcard.png';
import graph from '../../../assets/images/gra-ph.png';
import { Button } from '../../../components/button/onboardingButton';
import { Modal } from '../../../components/reuseables/prelaunch';
import { useState } from 'react';
import { motion } from 'motion/react';
import { modalVariants } from '../../../components/reuseables/animations/animations';
import gift from '../../../assets/animations/gift.gif';
import gift1 from '../../../assets/images/gift1.png';
import { SetGiftRegistry } from '../gift-registry/new-user/set-gift-registry';
import { GuestCards } from '../guestCards';
import CreateGuestList from '../create-guest-list/create-guest';
import { useNavigate } from 'react-router-dom';
// import { Avatar } from '../../../components/reuseables/avatar';

export const FirstTimer = ({
  completeEventData = {},
}: {
  completeEventData: any;
}) => {
  const [isFirstModalOpen, setIsFirstModalOpen] = useState(false);
  const [isSecondModalOpen, setIsSecondModalOpen] = useState(false);
  const [isGuestModalOpen, setIsGuestModalOpen] = useState(false);
  const [isSecondGuestModalOpen, setIsSecondGuestModalOpen] = useState(false);
  const [showGiftRegistry, setShowGiftRegistry] = useState(false);
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const navigate = useNavigate();
  // const openFirstModal = () => {
  //   setIsFirstModalOpen(true);
  //   setIsSecondModalOpen(false);
  // };

  const openSecondModal = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(true);
  };

  const closeAllModals = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(false);
  };

  const closeAllGuestModals = () => {
    setIsGuestModalOpen(false);
    setIsSecondGuestModalOpen(false);
  };

  const openSecondGuestModal = () => {
    setIsGuestModalOpen(false);
    setIsSecondGuestModalOpen(true);
  };

  const navigateToGiftRegistrySetup = () => {
    closeAllModals();
    setShowGiftRegistry(true);
  };

  const navigateToGuestListSetup = () => {
    closeAllGuestModals();
    setIsGuestListModalOpen(true);
  };

  // const allGuests = [
  //   {
  //     id: 1,
  //     firstName: 'John',
  //     lastName: 'Doe',
  //     email: '<EMAIL>',
  //   },
  //   {
  //     id: 2,
  //     firstName: 'Jane',
  //     lastName: 'Smith',
  //     email: '<EMAIL>',
  //   },
  //   {
  //     id: 3,
  //     firstName: 'Michael',
  //     lastName: 'Johnson',
  //     email: '<EMAIL>',
  //   },
  //   {
  //     id: 4,
  //     firstName: 'Emily',
  //     lastName: 'Williams',
  //     email: '<EMAIL>',
  //   },
  //   {
  //     id: 5,
  //     firstName: 'David',
  //     lastName: 'Brown',
  //     email: '<EMAIL>',
  //   },
  // ];

  // const guestList = allGuests.map((guest) => ({
  //   id: guest.id,
  //   initials: `${guest.firstName.charAt(0)}${guest.lastName.charAt(0)}`,
  // }));
  // const additionalGuests = Math.max(0, guestList.length - 3);
  const totalGuests = completeEventData?.guest_count?.total ?? 0;
  const cashGift = completeEventData?.gift_count?.total_cash_gifts ?? 0;
  const itemGift = completeEventData?.gift_count?.total_item_gifts ?? 0;

  return (
    <div className="px-4 md:px-0">
      <div className="flex flex-col md:flex-row gap-6 md:gap-0 justify-between mb-5">
        <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 md:max-w-[270px] w-full h-[320px] relative overflow-hidden">
          <div className="relative z-10  h-full ">
            <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
              GIFT REGISTRY
            </h2>
            {cashGift === 0 && itemGift === 0 ? (
              <>
                <p className="text-2xl mt-3 mb-5 font-medium">
                  Curate{' '}
                  <span className="italic text-transparent bg-clip-text bg-[linear-gradient(120.36deg,#FFBBA3_28.82%,#A6AAF9_67.72%)]">
                    Gifts
                  </span>{' '}
                  for <br />
                  your event
                </p>
                <Button
                  // onClick={openFirstModal}
                  onClick={() => navigate('/gift-registry-onboarding')}
                  variant="primary"
                  size="md"
                  className="bg-primary h-9 text-white">
                  Create Registry
                </Button>
              </>
            ) : (
              <p className="text-[64px] font-bold text-dark-blue-400">
                {cashGift}
                <span className="text-grey-900">/{itemGift}</span>
              </p>
            )}
          </div>
          <div className="absolute -bottom-7 blur-xs right-0 pointer-events-none ">
            <img src={box} alt="box" />
          </div>
        </div>

        <div className="bg-white shadow-[0_12px_120px_0_#5F5F5F0F] rounded-[14px] p-4 md:max-w-[270px] w-full h-[320px] relative overflow-hidden">
          <div className="relative z-10  h-full ">
            <h2 className="text-sm font-medium text-grey-250 tracking-[0.10em]">
              GUESTS
            </h2>
            {totalGuests > 0 ? (
              <>
                <p className="text-[64px] font-bold text-dark-blue-400">
                  {totalGuests || 0}
                </p>
                {/* <div className="flex items-center -mt-3">
                  {guestList.slice(0, 3).map((guest, index) => (
                    <Avatar
                      key={guest.id}
                      initials={guest.initials}
                      offset={index > 0}
                      color="bg-gradient-to-b from-[#F5F9FF] to-[#E1ECFE]"
                    />
                  ))}
                  {additionalGuests > 0 && (
                    <div className="bg-[linear-gradient(156.19deg,#FEFAF8_29.8%,#F5F6FE_65.36%,#F5F9FF_82.51%)] text-dark-blue-200 w-10 h-10 rounded-full flex items-center justify-center font-medium -ml-3 border-2 border-white">
                      +{additionalGuests}
                    </div>
                  )}
                  <div className="border border-dashed border-grey-200 rounded-full p-1">
                    <Add color="#A4A7AE" size="20" />
                  </div>
                </div> */}
              </>
            ) : (
              <>
                <p className="text-2xl mt-3 mb-5 font-medium">
                  Create a Guestlist
                  <br /> for your event
                </p>
                <Button
                  onClick={() => setIsGuestModalOpen(true)}
                  variant="primary"
                  size="md"
                  className="bg-[linear-gradient(190.72deg,#FF9975_25.22%,#FF5519_89.64%)] h-9 text-white">
                  Create Guest list
                </Button>
              </>
            )}

            {/* <>
              <p className="text-[64px] font-bold text-dark-blue-400">
                {completeEventData?.guest_count?.total || 0}
              </p>
              <div className="flex items-center -mt-3">
                {guestList.slice(0, 3).map((guest, index) => (
                  <Avatar
                    key={guest.id}
                    initials={guest.initials}
                    offset={index > 0}
                    color="bg-gradient-to-b from-[#F5F9FF] to-[#E1ECFE]"
                  />
                ))}
                {additionalGuests > 0 && (
                  <div className="bg-[linear-gradient(156.19deg,#FEFAF8_29.8%,#F5F6FE_65.36%,#F5F9FF_82.51%)] text-dark-blue-200 w-10 h-10 rounded-full flex items-center justify-center font-medium -ml-3 border-2 border-white">
                    +{additionalGuests}
                  </div>
                )}
                <div className="border border-dashed border-grey-200 rounded-full p-1">
                  <Add color="#A4A7AE" size="20" />
                </div>{' '}
              </div>
            </> */}

            {/* <>
              <p className="text-2xl mt-3 mb-5 font-medium">
                Create a Guestlist
                <br /> for your event
              </p>
              <Button
                onClick={() => setIsGuestModalOpen(true)}
                variant="primary"
                size="md"
                className="bg-[linear-gradient(190.72deg,#FF9975_25.22%,#FF5519_89.64%)] h-9 text-white">
                Create Guest list{' '}
              </Button>{' '}
            </> */}
          </div>
          <div className="absolute bottom-0 right-0 blur- pointer-events-none ">
            <img src={guestcard} alt="box" />
          </div>
        </div>
      </div>
      <div className="w-full mb-40 flex justify-center text-center sm:text-left sm:justify-between items-start max-w-[560px] sm:h-[205px] bg-white rounded-[14px]  ">
        <div className="pl-3 pt-3 pb-3 sm:pb-0">
          <div className="text-primary italic font-extrabold text-xs gap-1 mb-2 flex items-center">
            BUDGET PLANNER
            <ArrowDown2 color="#292D32" size={12} />
          </div>
          <p className="md:text-[28px] font-medium">
            Curate{' '}
            <span className="italic text-transparent bg-clip-text bg-[linear-gradient(120.36deg,#FFBBA3_28.82%,#A6AAF9_67.72%)]">
              Gifts
            </span>{' '}
            for <br />
            your event
          </p>

          <button
            type="button"
            className="mt-2 bg-gradient-to-r from-[#4D55F2] to-[#FF6630] rounded-full ">
            <div className="rounded-full bg-white border border-dashed text-nowrap text-sm font-semibold px-2 py-1">
              Create A Budget
            </div>
          </button>
        </div>
        <img
          src={graph}
          alt="graph"
          className="rounded-tr-[14px] hidden sm:block"
        />
      </div>
      <Modal
        isOpen={isFirstModalOpen}
        onClose={closeAllModals}
        actionButton={openSecondModal}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="Wish. Share. Receive."
        description={
          <p className="text-grey-950 text-lg">
            Create the perfect wishlist, share it with <br /> loved ones, and
            receive gifts you truly want.
            <br /> No duplicates, no guesswork—just joy!
          </p>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      <Modal
        isOpen={isSecondModalOpen}
        onClose={closeAllModals}
        actionButton={navigateToGiftRegistrySetup}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="WHAT TO EXPECT"
        description={
          <div className="mt-2">
            <p className="mb-5 text-base text-grey-550 font-normal">
              Here are some steps to follow to set up your gift registry
            </p>

            <div className="space-y-4 font-semibold italic">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Setup your account to receive payments{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Set up your Wallet{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Enter Delivery details for your gift items{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Start curating gifts{' '}
                </p>
              </div>
            </div>
          </div>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      <Modal
        isOpen={isGuestModalOpen}
        onClose={closeAllGuestModals}
        actionButton={openSecondGuestModal}
        forwardActionText="Continue"
        secondModalAnimation={true}
        title="Guestlist "
        name="Manager"
        subtitle="Effortless Invites, Perfect Attendance"
        description={
          <p>
            Send invites, track RSVPs, and manage <br /> your guest list
            seamlessly—all in one place.
            <br /> No stress, just perfect planning!
          </p>
        }
        leftContent={<GuestCards />}
        leftContentMobile={true}
      />
      <Modal
        isOpen={isSecondGuestModalOpen}
        onClose={closeAllGuestModals}
        actionButton={navigateToGuestListSetup}
        forwardActionText="Continue"
        secondModalAnimation={true}
        title="Guestlist "
        name="Manager"
        subtitle="WHAT TO EXPECT"
        description={
          <div className="mt-2">
            <p className="mb-5 text-base text-grey-550 font-normal">
              Here are some steps to follow to set up your guest manager
            </p>

            <div className="space-y-4 font-semibold italic">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Select Invite from pre designed IV templates or alternately
                  upload your own Invite{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Add Guests to your Guestlist{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Preview and Send out your Invites{' '}
                </p>
              </div>
            </div>
          </div>
        }
        leftContent={<GuestCards />}
        leftContentMobile={true}
      />
      {showGiftRegistry && (
        <SetGiftRegistry onClose={() => setShowGiftRegistry(false)} />
      )}
      {isGuestListModalOpen && (
        <CreateGuestList onClose={() => setIsGuestListModalOpen(false)} />
      )}
    </div>
  );
};
