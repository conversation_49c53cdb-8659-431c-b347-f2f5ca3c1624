import React, { useState, useEffect, useRef } from "react";
import { Card } from "../../../components/ui/card";
import { ClipLoader } from "react-spinners";
import {
  WalletAPI,
  Wallet,
  Transaction,
  PayoutBank,
  PayoutAccount,
  CreatePayoutAccountPayload,
  TransactionListParams,
  TransactionMeta,
} from "../../../lib/apis/walletapi";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { useUserAuthStore } from "../../../lib/store/auth";
import { AuthServices } from "../../../lib/services/auth";

// const WALLET_BALANCE = 120025;

const formatCurrency = (amount: number) =>
  `₦${amount.toLocaleString("en-NG", { maximumFractionDigits: 0 })}`;

interface WalletState {
  hasWallet: boolean;
  hasTransactions: boolean;
  hasWithdrawalAccounts: boolean;
}

interface WithdrawalAccountSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccountAdded: () => void;
  banks: PayoutBank[];
}

// Add new state interfaces for API data
interface TransactionState {
  transactions: Transaction[];
  meta: TransactionMeta | null;
  loading: boolean;
  error: string | null;
}

interface PayoutAccountState {
  accounts: PayoutAccount[];
  banks: PayoutBank[];
  loading: boolean;
  error: string | null;
}

const WithdrawalAccountSetupModal: React.FC<
  WithdrawalAccountSetupModalProps
> = ({ isOpen, onClose, onAccountAdded, banks }) => {
  const [selectedBank, setSelectedBank] = useState<PayoutBank | null>(null);
  const [accountNumber, setAccountNumber] = useState("");

  const [accountName, setAccountName] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedBank(null);
      setAccountNumber("");
      setAccountName("");
      setIsVerifying(false);
      setIsCreating(false);
    }
  }, [isOpen]);

  // Mock account verification (replace with real API when available)
  useEffect(() => {
    if (selectedBank && accountNumber && accountNumber.length === 10) {
      setIsVerifying(true);
      const timer = setTimeout(() => {
        setAccountName("ADE BOLUWATIFE"); // This would come from real account verification API
        setIsVerifying(false);
      }, 1200);
      return () => clearTimeout(timer);
    } else {
      setAccountName("");
      setIsVerifying(false);
    }
  }, [selectedBank, accountNumber]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      selectedBank &&
      accountNumber &&
      !isVerifying &&
      accountName &&
      !isCreating
    ) {
      setIsCreating(true);
      try {
        const payload: CreatePayoutAccountPayload = {
          account_number: accountNumber,
          bank_code: selectedBank.bank_code,
          currency_code: "ngn",
        };

        await WalletAPI.createPayoutAccount(payload);
        toast.success("Account added successfully!");
        onAccountAdded();
        onClose();
      } catch (error) {
        const errorMsg =
          typeof error === "object" && error && "message" in error
            ? (error as { message?: string }).message ?? "Failed to add account"
            : "Failed to add account";
        toast.error(errorMsg);
      } finally {
        setIsCreating(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed flex-col inset-0 z-50 flex items-center justify-center overflow-y-auto min-h-screen w-full bg-gradient-to-b from-[#FEFAF8] via-[#F5F6FE] to-[#F5F9FF]">
      <img
        className="absolute top-0 hidden md:block right-0 z-[1]"
        src="/wallet-setup.png"
        alt=""
      />
      {/* Cancel Button - Figma pill style, top right */}
      <button
        onClick={onClose}
        className="absolute top-10 right-16 z-20 bg-[#F5F6FE] text-[#4D55F2] font-rethink font-medium text-base rounded-full px-4 py-2 h-[44px]  border-none cursor-pointer"
        style={{ fontSize: 16 }}
      >
        Cancel
      </button>

      {/* Title - centered above card */}
      <div className="px-4 md:mt-[150px] w-full md:w-fit relative z-[2]">
        <span className="font-rethink font-medium text-[28px] sm:text-[40px] z-10 relative leading-[1.15]  tracking-[-0.03em] text-black">
          Add Account details
        </span>

        {/* Card */}
        <div className="relative md:mt-[114px] mt-[110px] flex flex-col items-center w-full  md:w-[550px] sm:max-w-[550px] h-auto sm:h-[552px] rounded-[20px] bg-white shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] p-4 sm:p-6 ">
          {/* Moneys Icon in 105x108px circle with 9px border, overlapping card */}
          <div className="w-[80px] h-[80px] sm:w-[105px] sm:h-[108px] rounded-full bg-[#FFEEE8] border-[6px] sm:border-[9px] border-white absolute -top-[40px] sm:-top-[54px] left-4 sm:left-9 flex items-center justify-center ">
            <img
              src="/assets/walle/moneys.svg"
              alt="Moneys"
              className="w-[48px] h-[50px] sm:w-[63px] sm:h-[66px]"
            />
          </div>

          {/* Info Box with Bank Icon */}
          <div className="flex items-center gap-3 bg-[#FEFAF8]  rounded-[16px] w-full sm:w-full px-6 mt-16 sm:mt-[64px] mb-0 px-3 sm:px-5 py-3">
            <img
              src="/assets/walle/bank.svg"
              alt="Bank"
              className="w-8 h-8 sm:w-10 sm:h-10 mr-2"
            />
            <span className="font-rethink  font-medium text-[#000030] italic text-[13px] sm:text-[14px] font-medium leading-[1.45] tracking-[-0.03em] flex-1">
              Adding your bank account helps you easily transfer funds from your
              wallet directly into your preferred bank account.
            </span>
          </div>

          {/* Form Card */}
          <form
            className="w-full sm:w-full mt-8 sm:mt-8 rounded-[20px] bg-white flex flex-col gap-6 sm:gap-5"
            style={{ borderRadius: 20 }}
            onSubmit={handleSubmit}
          >
            {/* Bank Dropdown */}
            <div className="">
              <label className="font-rethink text-[#374151] font-medium text-[14px] mb-2 block">
                Bank
              </label>
              <div className="relative">
                <select
                  value={selectedBank?.bank_code || ""}
                  onChange={(e) => {
                    const bank = banks.find(
                      (b) => b.bank_code === e.target.value
                    );
                    setSelectedBank(bank || null);
                  }}
                  className="w-full h-12 px-4 pr-10 border border-[#D5D7DA] rounded-full text-base font-normal outline-none focus:border-[#4D55F2] focus:ring-1 focus:ring-[#4D55F2] appearance-none bg-white transition-colors"
                  required
                  style={{ fontFamily: "Rethink Sans" }}
                >
                  <option value="">
                    {banks.length === 0 ? "Loading banks..." : "Select Bank"}
                  </option>
                  {banks.map((bank) => (
                    <option key={bank.bank_code} value={bank.bank_code}>
                      {bank.bank_name}
                    </option>
                  ))}
                </select>
                <svg
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 pointer-events-none"
                  width="12"
                  height="8"
                  viewBox="0 0 12 8"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 1.5L6 6.5L11 1.5"
                    stroke="#717680"
                    strokeWidth="1.66667"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {/* Account Number Input */}
            <div className="mb-4">
              <label className="font-rethink text-[#374151] font-medium text-[14px] mb-2 block">
                Account Number
              </label>
              <input
                type="text"
                value={accountNumber}
                onChange={(e) =>
                  setAccountNumber(e.target.value.replace(/\D/g, ""))
                }
                placeholder="e.g **********"
                maxLength={10}
                className="w-full h-12 px-4 border border-[#D5D7DA] rounded-full text-base font-normal outline-none focus:border-[#4D55F2] focus:ring-1 focus:ring-[#4D55F2] transition-colors"
                required
                disabled={isCreating}
                style={{ fontFamily: "Rethink Sans" }}
              />
              <span className="font-rethink text-[12px] text-[#535862] italic mt-1 block">
                Enter account number to receive cash gift
              </span>
              {isVerifying && (
                <div className="flex items-center gap-2 mt-2">
                  <div className="w-4 h-4 border-2 border-[#4D55F2] border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-[#4D55F2] text-sm font-medium">
                    Verifying account...
                  </span>
                </div>
              )}
            </div>

            {/* Account Name (Read-only, auto-filled) */}
            {accountName && !isVerifying && (
              <div className="">
                <div className="flex items-center w-fit gap-3 p-2 bg-[#F0FDF4] border border-[#BBF7D0] rounded-full">
                  <div className="w-5 h-5 bg-[#22C55E] rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10 3L4.5 8.5L2 6"
                        stroke="white"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <span className="font-rethink text-[#166534] font-medium text-[14px]">
                    {accountName}
                  </span>
                </div>
              </div>
            )}

            {/* Continue Button - Figma SVG */}
            <button
              type="submit"
              disabled={
                !selectedBank ||
                !accountNumber ||
                isVerifying ||
                !accountName ||
                isCreating
              }
              className={`w-[125px] h-12 sm:h-[44px] rounded-[100px] flex items-center justify-center mt-4 sm:mt-6 font-rethink font-semibold text-base text-white gap-2 border-none shadow-[0px_1px_2px_0px_rgba(10,13,18,0.05)]
              ${
                selectedBank &&
                accountNumber &&
                !isVerifying &&
                accountName &&
                !isCreating
                  ? "bg-[#343CD8] cursor-pointer opacity-100"
                  : "bg-[#343CD8]/50 cursor-not-allowed "
              }`}
            >
              {isCreating ? "Adding..." : "Continue"}
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                  fill="white"
                />
                <path
                  d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                  fill="white"
                />
              </svg>
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

// Wallet Setup Modal (Figma 12416-140016 inspired, similar to PinEntryModal)
interface WalletSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  setShowSetPinModal: React.Dispatch<React.SetStateAction<boolean>>;
}

interface WalletSetupModalState {
  isCreating: boolean;
  showSuccess: boolean;
  error: string | null;
  createdWallet: Wallet | null;
}

const WalletSetupModal: React.FC<
  WalletSetupModalProps & { onWalletCreated: (wallet: Wallet) => void }
> = ({ isOpen, onClose, setShowSetPinModal, onWalletCreated }) => {
  const [state, setState] = useState<WalletSetupModalState>({
    isCreating: false,
    showSuccess: false,
    error: null,
    createdWallet: null,
  });

  const creationAttemptedRef = useRef(false);

  useEffect(() => {
    if (isOpen && !creationAttemptedRef.current) {
      creationAttemptedRef.current = true;
      setState({
        isCreating: true,
        showSuccess: false,
        error: null,
        createdWallet: null,
      });
      WalletAPI.createWallet({ currency_code: "ngn" })
        .then((wallet: Wallet) => {
          setState({
            isCreating: false,
            showSuccess: true,
            error: null,
            createdWallet: wallet,
          });
          onWalletCreated(wallet);
        })
        .catch((err: unknown) => {
          const errorMsg =
            typeof err === "object" && err && "message" in err
              ? (err as { message?: string }).message ??
                "Failed to create wallet"
              : "Failed to create wallet";
          setState({
            isCreating: false,
            showSuccess: false,
            error: errorMsg,
            createdWallet: null,
          });
          if (
            typeof err === "object" &&
            err &&
            "code" in err &&
            (err as { code?: string }).code === "wallet_exists"
          ) {
            toast.info("Wallet already exists. Loading your wallet...");
            WalletAPI.getUserWallets()
              .then((wallets) => {
                if (wallets && wallets.length > 0) {
                  setShowSetPinModal(false);
                  onWalletCreated(wallets[0]);
                  setState({
                    isCreating: false,
                    showSuccess: true,
                    error: null,
                    createdWallet: wallets[0],
                  });
                }
              })
              .catch((fetchErr: unknown) => {
                toast.error(
                  typeof fetchErr === "object" &&
                    fetchErr &&
                    "message" in fetchErr
                    ? (fetchErr as { message?: string }).message ??
                        "Failed to load existing wallet"
                    : "Failed to load existing wallet"
                );
              });
          } else {
            toast.error(errorMsg);
          }
        });
    }
  }, [isOpen]);

  // Reset the attempt flag when modal closes
  useEffect(() => {
    if (!isOpen) {
      creationAttemptedRef.current = false;
    }
  }, [isOpen]);

  if (!isOpen) return null;
  if (state.isCreating) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl p-4 sm:p-8 w-full max-w-xs sm:max-w-[522px] h-[340px] sm:h-[456px] mx-4 relative flex flex-col items-center justify-center">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 sm:top-6 sm:right-6 hover:text-grey-50 transition-colors cursor-pointer"
            aria-label="Close"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>
          <div className="flex flex-col items-center justify-center mt-10 sm:mt-20">
            <ClipLoader size={80} color="#CACCFB" />
            <h2 className="text-[20px] sm:text-[28px] font-semibold text-center text-grey-50 mt-6 sm:mt-8">
              Creating wallet...
            </h2>
            {state.error && (
              <div className="text-red-500 mt-4 text-center">{state.error}</div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (state.showSuccess) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl w-full max-w-xs sm:max-w-[522px] h-[420px] md:h-[540px]  mx-4 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 sm:top-6 sm:right-6 hover:text-grey-50 transition-colors cursor-pointer"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>
          {/* Success Illustration */}
          <div className="text-center">
            <div className="flex justify-center rounded-t-3xl mb-4 sm:mb-6 p-4 sm:p-8 pt-[40px] sm:pt-[60px] bg-[#F5F6FE] h-[180px] sm:h-[280px]">
              <div className="relative">
                {/* Enhanced money bills and coins illustration */}
                <img
                  src="/folder.png"
                  alt=""
                  className="w-[140px] sm:w-[220px] h-auto"
                />
              </div>
            </div>
            <h2 className="text-[24px] sm:text-[28px] leading-normal font-semibold text-grey-50 mb-2">
              Your Wallet has Successfully
            </h2>
            <p className="text-[20px] sm:text-[24px] leading-normal mt-[-3px] text-[#808080] mb-2">
              been created
            </p>
            <p className="font-rethink text-[16px] text-center mx-auto font-normal leading-[1.5] tracking-[-0.03em] text-[#808080]  mb-8 max-w-[372px]">
              Click the button below to set your transaction pin
            </p>
            <div className="flex justify-center mt-3 pt-6 border-t border-t-gray-200 items-center">
              <button
                onClick={() => {
                  setShowSetPinModal(true);
                }}
                className="w-[200px] flex items-center justify-center px-6 rounded-full font-bold text-base bg-[#4D55F2] h-[40px] text-white hover:bg-[#343CD8] transition-all shadow-sm"
              >
                Set Transaction Pin
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Secure Your Wallet Modal (Figma 12416-140026 inspired)
interface SecureWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateWallet: () => void;
}

const SecureWalletModal: React.FC<SecureWalletModalProps> = ({
  isOpen,
  onClose,
  onCreateWallet,
}) => {
  if (!isOpen) return null;
  return (
    <div className="fixed flex-col inset-0 z-50 flex items-center justify-center overflow-y-auto min-h-screen w-full bg-gradient-to-b from-[#FEFAF8] via-[#F5F6FE] to-[#F5F9FF]">
      {/* Cancel Button - Figma pill style, top right */}
      <img
        className="absolute top-0 hidden md:block right-0 z-[1]"
        src="/wallet-setup.png"
        alt=""
      />
      <button
        onClick={onClose}
        className="absolute top-10 right-16 z-20 bg-[#F5F6FE] text-[#4D55F2] font-rethink font-medium text-base rounded-full px-4 h-[44px] py-2  border-none cursor-pointer"
      >
        Cancel
      </button>
      {/* Title - centered above card */}
      <div className="px-4 md:mt-[100px] w-full relative z-[2] md:w-fit">
        <span className="font-rethink font-medium text-[28px] sm:text-[40px] z-10 relative leading-[1.15]  tracking-[-0.03em] text-black">
          Secure your Wallet
        </span>

        {/* Card */}
        <div className="relative flex flex-col md:mt-[114px] mt-[110px] items-start w-full md:w-[550px] h-[381px] z-[2] rounded-[20px] bg-white shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] ">
          {/* Wallet Icon in 105x108px circle with 9px border, overlapping card */}
          <div className="w-[105px] h-[108px] rounded-full bg-[#FFE5E5] border-[9px] border-white absolute -top-[54px] left-9 flex items-center justify-center shadow-sm">
            <svg
              width="64"
              height="66"
              viewBox="0 0 64 66"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M50.7828 37.1777L54.6027 37.311L54.6864 37.3208C55.0994 37.3893 55.3773 37.7359 55.3625 38.1613L55.2701 40.8072C55.2541 41.2629 54.9125 41.5966 54.4889 41.6036L50.786 41.4744C49.6787 41.4358 48.7271 40.5502 48.6844 39.4546L48.6838 39.4448L48.6827 39.1965C48.7143 38.6237 48.9688 38.1058 49.3432 37.7421L49.3533 37.7327C49.7236 37.3532 50.2284 37.1585 50.7828 37.1777Z"
                fill="#CC2200"
                stroke="#B20900"
              />
              <path
                d="M47.1545 35.3585C45.8387 36.6104 45.1613 38.5203 45.5993 40.5486C46.174 43.0319 48.4318 44.6734 50.8712 44.7586L53.0819 44.8357C54.4794 44.8845 55.5813 46.1149 55.5304 47.5698L55.5129 48.0725C55.3215 53.5486 50.871 57.8695 45.6111 57.6859L16.4402 56.6678C11.1803 56.4842 7.04218 51.8635 7.23352 46.3874L7.85558 28.5836C7.96927 25.3297 9.56922 22.4985 11.9444 20.7803C13.5896 19.5663 15.5975 18.8948 17.7574 18.9702L46.9283 19.9883C52.1882 20.1719 56.3263 24.7926 56.1349 30.2686L56.0943 31.4326C56.0434 32.8876 54.8584 34.0382 53.4608 33.9894L50.869 33.8989C49.446 33.8493 48.1298 34.386 47.1545 35.3585Z"
                fill="#CC2200"
              />
              <path
                d="M43.3326 14.3798C43.9937 15.118 43.3705 16.2087 42.4049 16.175L22.8908 15.4674C21.7727 15.4284 21.2382 13.9795 22.08 13.1877L26.3471 9.01934C29.954 5.54303 35.5951 5.73991 38.9506 9.45922L43.2337 14.2969C43.2582 14.3242 43.3081 14.3524 43.3326 14.3798Z"
                fill="#FF9999"
              />
            </svg>
          </div>
          {/* Description */}
          <div className="w-full sm:px-5 mt-[80px] px-5 text-left font-rethink text-[#999999] text-[18px] font-normal leading-[1.7] tracking-[-0.03em]">
            Easily manage your gift contributions and event payments.
            <br />
            <br />
            <span className="text-black pr-5">
              Set up your wallet to get started—secure, simple, and seamless.
            </span>
          </div>
          {/* Create Wallet Button */}
          <button
            onClick={onCreateWallet}
            className="flex items-center ml-4 mt-[100px] justify-center gap-2  bg-[#343CD8] text-white font-rethink font-semibold text-base rounded-full px-7 py-3 shadow-sm border-none min-w-[180px] cursor-pointer"
          >
            Create Wallet
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                fill="white"
              />
              <path
                d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                fill="white"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

// --- SetTransactionPinModal Component ---
interface SetTransactionPinModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  walletId: string | null;
  onPinSet?: () => void;
}

const SetTransactionPinModal: React.FC<SetTransactionPinModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  walletId,
  onPinSet,
}) => {
  const [newPin, setNewPin] = useState(["", "", "", ""]);
  const [isSetting, setIsSetting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Focus next input on change
  const handlePinInputChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return; // Only allow single digit
    const updatedPin = [...newPin];
    updatedPin[index] = value;
    setNewPin(updatedPin);
    if (value && index < 3) {
      const nextInput = document.getElementById(`set-pin-input-${index + 1}`);
      if (nextInput) (nextInput as HTMLInputElement).focus();
    }
  };
  const handleSetPin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newPin.join("").length === 4 && walletId) {
      setIsSetting(true);
      setError(null);
      try {
        await WalletAPI.setWalletPin(walletId, { pin: newPin.join("") });
        setIsSetting(false);
        if (onPinSet) {
          await onPinSet(); // Call refresh user data
        }
        onSuccess();
        setNewPin(["", "", "", ""]);
      } catch (err: unknown) {
        setIsSetting(false);
        if (
          err &&
          typeof err === "object" &&
          "message" in err &&
          typeof (err as { message?: unknown }).message === "string"
        ) {
          setError((err as { message: string }).message);
        } else {
          setError("Failed to set pin");
        }
      }
    }
  };
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex flex-col items-cent justify-start min-h-screen w-full bg-white ">
      <div className=" flex flex-col items-center md:max-w-[560px] w-full mx-auto relative">
        <button
          onClick={onClose}
          className="absolute top-10 right-0 z-20 bg-[#F5F6FE] text-[#4D55F2] font-rethink font-medium text-base rounded-full px-4 h-[44px] py-2 border-none cursor-pointer"
          aria-label="Cancel"
        >
          Cancel
        </button>
        <div className="px-4 mt-[100px] w-full md:w-fit relative z-[2]">
          <div className="font-rethink mb-[105px] font-medium text-[28px] sm:text-[40px] z-10 relative leading-[1.15] tracking-[-0.03em] text-black">
            Set Transaction Pin
          </div>
          <div className="bg-[#FAF9FF] p-5 h-[84px] mt-8 rounded-xl">
            <p className="text-black italic font-medium mb-[40px] text-sm sm:text-base">
              Create your preferred 4 digit transaction pin to securely perform
              your transactions
            </p>
          </div>
          <form
            onSubmit={handleSetPin}
            className="flex flex-col items-center mt-[48px] sm:mt-[64px] mb-[36px] sm:mb-[52px] gap-8"
          >
            <div className="flex justify-center gap-2 sm:gap-4">
              {newPin.map((digit, index) => (
                <div key={index} className="relative">
                  <input
                    id={`set-pin-input-${index}`}
                    type="password"
                    inputMode="numeric"
                    autoComplete="one-time-code"
                    pattern="[0-9]*"
                    value={digit}
                    onChange={(e) =>
                      handlePinInputChange(index, e.target.value)
                    }
                    className="w-12 sm:w-16 h-12 sm:h-16 text-center text-xl sm:text-2xl font-bold border-2 border-[#D5D7DA] rounded-full focus:border-primary outline-none"
                    maxLength={1}
                    aria-label={`Pin digit ${index + 1}`}
                    disabled={isSetting}
                  />
                </div>
              ))}
            </div>
            <button
              type="submit"
              disabled={newPin.join("").length !== 4 || isSetting}
              className={`w-full py-3 sm:py-4 px-4 mt-[48px] sm:px-6 flex gap-2 items-center justify-center rounded-full font-bold text-base sm:text-lg transition-all ${
                newPin.join("").length === 4 && !isSetting
                  ? "bg-primary text-white hover:bg-primary/90"
                  : "bg-primary/50 text-white hover:bg-primary/90 cursor-not-allowed"
              }`}
            >
              {isSetting ? "Setting Pin..." : "Set Pin"}
            </button>
            {error && (
              <div className="text-red-500 mt-4 text-center">{error}</div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

// --- PinSuccessModal Component ---
const PinSuccessModal: React.FC<{ isOpen: boolean; onClose: () => void }> = ({
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl text-center shadow-xl h-[430px] sm:h-[596px] md:w-[522px] w-full">
        {/* Success Icon */}
        <div className="flex justify-center bg-gradient-to-b rounded-t-3xl mb-6 p-8 pt-[40px] sm:pt-[60px] from-[#FDEFE9] to-[#FEF7F4] h-[180px] sm:h-[270px]">
          <div className="relative">
            <div className="text-[60px] sm:text-[100px]">🎉</div>
          </div>
        </div>
        <div className="p-4 sm:p-8">
          <h2 className="text-[20px] sm:text-[28px] leading-normal font-semibold text-grey-50 mb-2">
            Your Transaction pin has
          </h2>
          <p className="text-[16px] sm:text-[24px] leading-normal mt-[-3px] text-[#808080] mb-2">
            been set successfully
          </p>
          <p className="text-[14px] sm:text-[16px] text-[#808080] mb-6 max-w-[450px] mx-auto">
            You're all set. You can now safely withdraw your funds into your
            account.
          </p>
          {/* Continue Button */}
          <button
            onClick={onClose}
            className="bg-primary text-white py-2 sm:py-3 mt-4 sm:mt-[30px] px-6 sm:px-8 rounded-full font-medium hover:bg-primary/70 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

const WalletPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"transactions" | "withdrawal">(
    "transactions"
  );

  const [walletState, setWalletState] = useState<WalletState>({
    hasWallet: false,
    hasTransactions: false,
    hasWithdrawalAccounts: false,
  });
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Get user data from store
  const { userData, setAuthData, userAppToken } = useUserAuthStore();

  // New state for API data
  const [transactionState, setTransactionState] = useState<TransactionState>({
    transactions: [],
    meta: null,
    loading: false,
    error: null,
  });

  const [payoutAccountState, setPayoutAccountState] =
    useState<PayoutAccountState>({
      accounts: [],
      banks: [],
      loading: false,
      error: null,
    });

  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);
  const [showWalletSetupModal, setShowWalletSetupModal] = useState(false);
  const [showSecureWalletModal, setShowSecureWalletModal] = useState(false);
  // Add state for set pin modal and pin success
  const [showSetPinModal, setShowSetPinModal] = useState(false);
  const navigate = useNavigate();
  const [showPinSuccessModal, setShowPinSuccessModal] = useState(false);
  const [createdWalletId, setCreatedWalletId] = useState<string | null>(null);
  const [isTopupLoading, setIsTopupLoading] = useState(false);

  // Transaction filtering and pagination state
  const [transactionFilters, setTransactionFilters] = useState({
    page: 1,
    per_page: 25,
    from: "",
    to: "",
    search: "",
  });
  const [showDateFilters, setShowDateFilters] = useState(false);

  // Function to refresh user data
  const refreshUserData = async () => {
    try {
      const userResponse = await AuthServices.getUser();
      const latestUserData = userResponse.data;

      if (userAppToken) {
        setAuthData(userAppToken, {
          email: latestUserData.email,
          first_name: latestUserData.first_name,
          last_name: latestUserData.last_name,
          id: latestUserData.id,
          password_set: latestUserData.password_set,
          profile_picture:
            latestUserData.profile_picture_url ||
            latestUserData.profile_picture,
        });
      }
    } catch (error) {
      console.error("Failed to refresh user data:", error);
    }
  };

  // Function to handle wallet topup
  const handleTopup = async () => {
    if (!wallet) return;

    setIsTopupLoading(true);
    try {
      await WalletAPI.topUpWallet(wallet.id, { amount: "100.00" });
      toast.success("Topup successful!");

      // Refresh wallet data
      const wallets = await WalletAPI.getUserWallets();
      if (wallets && wallets.length > 0) {
        setWallet(wallets[0]);
      }
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Failed to topup wallet"
          : "Failed to topup wallet";
      toast.error(errorMsg);
    } finally {
      setIsTopupLoading(false);
    }
  };

  // Fetch transactions
  const fetchTransactions = async (filters = transactionFilters) => {
    if (!wallet) return;

    setTransactionState((prev) => ({ ...prev, loading: true, error: null }));
    try {
      // Prepare API parameters
      const params: TransactionListParams = {
        page: filters.page,
        per_page: filters.per_page,
      };

      // Add date filters if provided
      if (filters.from) {
        params.from = filters.from;
      }
      if (filters.to) {
        params.to = filters.to;
      }

      const response = await WalletAPI.getUserTransactions(params);

      // Filter transactions by search term on frontend if search is provided
      let filteredTransactions = response.transactions;
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredTransactions = response.transactions.filter(
          (transaction) =>
            transaction.description.toLowerCase().includes(searchTerm) ||
            transaction.type.toLowerCase().includes(searchTerm) ||
            transaction.status.toLowerCase().includes(searchTerm)
        );
      }

      setTransactionState({
        transactions: filteredTransactions,
        meta: response.meta,
        loading: false,
        error: null,
      });
      // Only update hasTransactions if this is the initial load (no filters applied)
      const hasActiveFilters = filters.search || filters.from || filters.to;
      if (!hasActiveFilters) {
        setWalletState((prev) => ({
          ...prev,
          hasTransactions: filteredTransactions.length > 0,
        }));
      }
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to fetch transactions"
          : "Failed to fetch transactions";
      setTransactionState((prev) => ({
        ...prev,
        loading: false,
        error: errorMsg,
      }));
      toast.error(errorMsg);
    }
  };

  // Fetch payout accounts
  const fetchPayoutAccounts = async () => {
    setPayoutAccountState((prev) => ({
      ...prev,
      loading: true,
      error: null,
    }));
    try {
      // Fetch both accounts and banks in parallel
      const [accounts, banks] = await Promise.all([
        WalletAPI.getPayoutAccounts(),
        WalletAPI.getPayoutBanks("ngn"),
      ]);

      console.log("Fetched accounts:", accounts);
      console.log("Fetched banks:", banks);

      setPayoutAccountState((prev) => ({
        ...prev,
        accounts,
        banks,
        loading: false,
        error: null,
      }));
      setWalletState((prev) => ({
        ...prev,
        hasWithdrawalAccounts: accounts.length > 0,
      }));
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to fetch payout accounts"
          : "Failed to fetch payout accounts";
      setPayoutAccountState((prev) => ({
        ...prev,
        loading: false,
        error: errorMsg,
      }));
      toast.error(errorMsg);
    }
  };

  // On mount, check if wallet exists
  useEffect(() => {
    setIsInitialLoading(true);
    WalletAPI.getUserWallets()
      .then((wallets) => {
        if (wallets && wallets.length > 0) {
          setWalletState((prev) => ({ ...prev, hasWallet: true }));
          setCreatedWalletId(wallets[0].id);
          setWallet(wallets[0]);
        } else {
          setWalletState((prev) => ({ ...prev, hasWallet: false }));
          setCreatedWalletId(null);
          setWallet(null);
        }
      })
      .catch((err: unknown) => {
        toast.error(
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ??
                "Failed to fetch wallet info"
            : "Failed to fetch wallet info"
        );
      })
      .finally(() => {
        setIsInitialLoading(false);
      });
  }, []);

  // Fetch transactions and payout accounts when wallet is available
  useEffect(() => {
    if (wallet) {
      fetchTransactions();
      fetchPayoutAccounts();
    }
  }, [wallet]); // fetchTransactions and fetchPayoutAccounts are stable functions

  const EmptyWallet = () => (
    <div className="flex flex-col items-center gap-10 py-[72px]">
      <div className="relative w-[240px] h-[240px]">
        <div className="absolute inset-0 rounded-full ">
          <div className="absolute inset-[30px]  flex items-center justify-center">
            <svg
              width="201"
              height="200"
              viewBox="0 0 201 200"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M183.831 99.9987V141.665C183.831 166.665 167.164 183.332 142.164 183.332H58.8307C33.8307 183.332 17.1641 166.665 17.1641 141.665V99.9987C17.1641 77.332 30.8307 61.4987 52.0807 58.832C54.2474 58.4987 56.4974 58.332 58.8307 58.332H142.164C144.331 58.332 146.414 58.4153 148.414 58.7486C169.914 61.2486 183.831 77.1654 183.831 99.9987Z"
                fill="#B8BBFA"
              />
              <path
                d="M148.419 58.7513C146.419 58.418 144.336 58.3347 142.169 58.3347H58.8359C56.5026 58.3347 54.2526 58.5014 52.0859 58.8347C53.2526 56.5014 54.9193 54.3347 56.9193 52.3347L84.0026 25.168C95.4193 13.8346 113.919 13.8346 125.336 25.168L139.919 39.9181C145.253 45.1681 148.086 51.8346 148.419 58.7513Z"
                fill="#B8BBFA"
              />
              <path
                d="M183.831 104.168H158.831C149.664 104.168 142.164 111.668 142.164 120.835C142.164 130.001 149.664 137.501 158.831 137.501H183.831"
                fill="#B8BBFA"
              />
            </svg>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center gap-3">
        <h2 className="text-[28px] font-medium leading-[1.32] tracking-[-0.045em] text-black">
          You haven't created a Wallet yet
        </h2>
        <p className="text-center text-[16px] leading-[1.6] tracking-[-0.03em] text-[#808080] max-w-[400px]">
          Create a wallet to start receiving your money directly into your Bank
          Account.
          <br />
          Get started by clicking the button below
        </p>
      </div>
      <button
        className="px-4 py-2.5 bg-[#4D55F2] text-white rounded-full font-semibold text-sm"
        onClick={() => {
          if (!wallet) {
            setShowSecureWalletModal(true); // triggers wallet creation, then pin setup
          } else if (wallet && !userData?.password_set) {
            setShowSetPinModal(true); // skip wallet creation, go straight to pin setup
          }
        }}
      >
        Setup your Wallet
      </button>
    </div>
  );

  const EmptyTransactions = () => (
    <div className="flex flex-col items-center gap-10 py-[44px]">
      <div className="relative w-[240px] h-[240px]">
        <div className="absolute inset-0 rounded-full border border-[#EDEEFE]">
          <div className="absolute inset-[30px] rounded-full bg-gradient-to-b from-[#EDEEFE] to-[#F5F6FE] flex items-center justify-center">
            <svg
              width="180"
              height="180"
              viewBox="0 0 180 180"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_12422_141357)">
                <rect
                  width="180"
                  height="180"
                  rx="90"
                  fill="url(#paint0_linear_12422_141357)"
                />
                <path
                  d="M165 88V106.15C165 118 157.5 125.5 145.65 125.5H120V73.075C120 64.75 126.825 58 135.15 58C143.325 58.075 150.825 61.375 156.225 66.775C161.625 72.25 165 79.75 165 88Z"
                  fill="#CACCFB"
                />
                <path
                  opacity="0.4"
                  d="M15 95.5V200.5C15 206.725 22.05 210.25 27 206.5L39.825 196.9C42.825 194.65 47.025 194.95 49.725 197.65L62.1749 210.175C65.0999 213.1 69.9001 213.1 72.8251 210.175L85.425 197.575C88.05 194.95 92.25 194.65 95.175 196.9L108 206.5C112.95 210.175 120 206.65 120 200.5V73C120 64.75 126.75 58 135 58H52.5H45C22.5 58 15 71.425 15 88V95.5Z"
                  fill="#CACCFB"
                />
                <path
                  d="M90 116.125H45C41.925 116.125 39.375 113.575 39.375 110.5C39.375 107.425 41.925 104.875 45 104.875H90C93.075 104.875 95.625 107.425 95.625 110.5C95.625 113.575 93.075 116.125 90 116.125Z"
                  fill="#CACCFB"
                />
                <path
                  d="M84.375 146.125H50.625C47.55 146.125 45 143.575 45 140.5C45 137.425 47.55 134.875 50.625 134.875H84.375C87.45 134.875 90 137.425 90 140.5C90 143.575 87.45 146.125 84.375 146.125Z"
                  fill="#CACCFB"
                />
              </g>
              <defs>
                <linearGradient
                  id="paint0_linear_12422_141357"
                  x1="90"
                  y1="0"
                  x2="90"
                  y2="180"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EDEEFE" />
                  <stop offset="1" stop-color="#F5F6FE" />
                </linearGradient>
                <clipPath id="clip0_12422_141357">
                  <rect width="180" height="180" rx="90" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center gap-2">
        <h2 className="text-[28px] font-medium leading-[1.32] tracking-[-0.045em] text-black">
          No Transactions yet
        </h2>
        <p className="text-center text-[16px] leading-[1.5] tracking-[-0.03em] text-[#808080]">
          Your activity will appear here once you start transacting.
        </p>
      </div>
    </div>
  );

  const EmptyWithdrawalAccounts = () => (
    <div className="flex flex-col items-center gap-10 py-[44px]">
      <div className="relative w-[240px] h-[240px]">
        <div className="absolute inset-0 rounded-full border border-[#EDEEFE]">
          <div className="absolute inset-[30px] rounded-full bg-gradient-to-b from-[#EDEEFE] to-[#F5F6FE] flex items-center justify-center">
            <svg
              width="180"
              height="180"
              viewBox="0 0 180 180"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_12422_141803)">
                <rect
                  width="180"
                  height="180"
                  rx="90"
                  fill="url(#paint0_linear_12422_141803)"
                />
                <path
                  d="M165 174.5V197H15V174.5C15 170.375 18.375 167 22.5 167H157.5C161.625 167 165 170.375 165 174.5Z"
                  fill="#DBDDFC"
                  stroke="#CACCFB"
                  stroke-width="1.5"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  opacity="0.6"
                  d="M60 114.5H30V167H60V114.5Z"
                  fill="#DBDDFC"
                />
                <path
                  opacity="0.4"
                  d="M90 114.5H60V167H90V114.5Z"
                  fill="#DBDDFC"
                />
                <path
                  opacity="0.6"
                  d="M120 114.5H90V167H120V114.5Z"
                  fill="#DBDDFC"
                />
                <path
                  opacity="0.4"
                  d="M150 114.5H120V167H150V114.5Z"
                  fill="#CACCFB"
                />
                <path
                  d="M160.275 75.1256L92.775 48.1258C91.275 47.5258 88.725 47.5258 87.225 48.1258L19.725 75.1256C17.1 76.1756 15 79.2505 15 82.1005V107.001C15 111.126 18.375 114.501 22.5 114.501H157.5C161.625 114.501 165 111.126 165 107.001V82.1005C165 79.2505 162.9 76.1756 160.275 75.1256ZM90 95.7506C83.775 95.7506 78.75 90.7256 78.75 84.5006C78.75 78.2756 83.775 73.2506 90 73.2506C96.225 73.2506 101.25 78.2756 101.25 84.5006C101.25 90.7256 96.225 95.7506 90 95.7506Z"
                  fill="#CACCFB"
                />
              </g>
              <defs>
                <linearGradient
                  id="paint0_linear_12422_141803"
                  x1="90"
                  y1="0"
                  x2="90"
                  y2="180"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EDEEFE" />
                  <stop offset="1" stop-color="#F5F6FE" />
                </linearGradient>
                <clipPath id="clip0_12422_141803">
                  <rect width="180" height="180" rx="90" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center gap-2">
        <h2 className="text-[28px] font-medium leading-[1.32] tracking-[-0.045em] text-black">
          No Accounts added
        </h2>
        <p className="text-center text-[16px] leading-[1.5] tracking-[-0.03em] text-[#808080]">
          No withdrawal account added yet,
          <br />
          Click the button below to add one
        </p>
      </div>
      <button
        className="px-4 py-2.5 bg-[#4D55F2] text-white rounded-full font-semibold text-sm"
        onClick={() => setShowWithdrawalModal(true)}
      >
        Add Withdrawal Account
      </button>
    </div>
  );

  return (
    <div
      className="w-full min-h-screen bg-gradient-to-b from-[#FEFAF8] to-[#F5F6FE]"
      style={{ minHeight: "100dvh", marginTop: 90 }}
    >
      <div className="px-2 sm:px-4">
        {/* Wallet Heading */}
        <h1
          className="text-[20px] sm:text-[24px] pt-[90px] font-semibold max-w-full sm:max-w-[800px] mx-auto leading-[1] tracking-[-0.03em] text-[#000] mb-6 mt-0"
          style={{ fontFamily: "Rethink Sans" }}
        >
          Wallet
        </h1>

        {isInitialLoading ? (
          <Card className="bg-white rounded-2xl max-w-full sm:max-w-[788px] mx-auto shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] border-0">
            <div className="flex flex-col items-center gap-10 py-[72px]">
              <ClipLoader size={60} color="#4D55F2" />
              <p className="text-[16px] text-[#808080]">Loading wallet...</p>
            </div>
          </Card>
        ) : !wallet || (wallet && !userData?.password_set) ? (
          <Card className="bg-white rounded-2xl max-w-full sm:max-w-[788px] mx-auto shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] border-0">
            <EmptyWallet />
          </Card>
        ) : (
          <>
            {/* Balance Card */}
            <div className="bg-white rounded-2xl shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] px-0 py-0 min-h-[174px] overflow-visible relative w-full max-w-full sm:max-w-[800px] mx-auto">
              <div className="flex flex-col md:flex-row items-center md:items-start mx-auto gap-4 md:gap-8 px-2 sm:px-8 py-6 sm:py-8">
                {/* SVG Image */}
                <div
                  className="flex-shrink-0 flex items-center justify-center mb-4 md:mb-0"
                  style={{ minWidth: 104 }}
                >
                  <svg
                    width="181"
                    height="104"
                    viewBox="0 0 181 104"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clip-path="url(#clip0_11404_191009)">
                      <rect width="181" height="104" rx="16" fill="#000073" />
                      <path
                        d="M173.808 120.792L177.556 107.001C161.955 100.962 133.508 92.1686 109.886 96.8954C102.807 88.5324 94.4173 81.4404 85.0446 76.034C68.5922 66.5491 50.0641 62.6118 32.1574 64.6902C23.7703 51.011 11.9037 41.0487 -0.229476 38.3844L-2.41681 52.6447C4.77417 54.2258 12.2013 59.7936 18.2857 67.5345C-7.50161 75.2676 -13.0824 92.0927 -15.0237 97.9294C-21.0942 116.191 -14.9208 134.568 -0.354764 141.595C10.3009 146.719 25.1547 146.423 34.3098 134.199C43.8922 121.396 46.3188 103.384 40.9762 84.769C40.386 82.7115 39.7191 80.675 38.9671 78.6903C60.0117 77.9113 80.6461 86.5253 96.0867 101.665C86.1031 106.912 78.058 115.992 73.8588 130.709C73.8088 130.893 73.7547 131.093 73.7047 131.276C69.7853 145.696 72.9676 161.764 81.6946 171.425C91.454 182.249 106.538 183.82 117.574 175.17C130.199 165.271 134.335 145.668 127.638 127.495C125.403 121.413 122.623 115.612 119.363 110.167C120.331 110.101 121.309 110.056 122.312 110.021C141.501 109.489 162.816 116.538 173.82 120.797L173.808 120.792ZM105.661 112.857C110.074 119.019 113.757 125.881 116.489 133.324C120.089 143.101 119.755 156.08 110.867 163.038C104.446 168.072 95.6376 167.118 89.9214 160.79C84.6223 154.911 82.7158 144.435 85.3224 135.367C88.5542 124.052 95.2487 116.6 105.648 112.852L105.661 112.857ZM26.0734 80.3382C27.4337 83.2697 28.577 86.3204 29.4733 89.4449C31.7555 97.3907 34.2313 112.508 25.2497 124.511C20.0417 131.454 10.6872 131.254 4.18582 128.127C-5.45063 123.483 -6.73002 112.318 -3.72633 103.251C0.260663 91.2454 9.73938 83.94 26.0776 80.3229L26.0734 80.3382Z"
                        fill="#4D55F2"
                      />
                      <path
                        d="M347.619 115.153L355.291 103.911C338.778 87.8619 308.128 60.7947 279.753 50.2339C273.627 37.3819 265.681 24.9433 256.193 13.5399C239.535 -6.47197 219.138 -22.328 197.994 -31.8242C191.652 -50.7863 180.396 -68.3119 167.06 -78.7804L161.073 -66.0662C168.977 -59.8598 176.184 -49.5519 181.315 -37.958C149.715 -46.9381 139.193 -33.8731 135.538 -29.3442C124.103 -15.1726 126.752 7.01491 141.832 23.375C152.868 35.3266 170.06 44.618 183.582 38.4175C197.738 31.9183 204.912 15.6421 203.279 -6.24427C203.098 -8.66329 202.825 -11.1109 202.44 -13.562C226.885 -0.753918 248.573 21.0939 262.69 46.054C249.909 44.8092 238.429 48.6118 230.013 60.481C229.911 60.6309 229.8 60.7933 229.697 60.9432C221.676 72.6976 221.439 90.6673 229.149 105.869C237.767 122.888 254.77 134.177 269.592 132.73C286.548 131.072 296.079 114.323 292.777 91.9996C291.679 84.5329 289.885 76.9926 287.451 69.4954C288.583 70.0556 289.721 70.6416 290.885 71.2539C313.131 83.1097 335.985 103.846 347.633 115.166L347.619 115.153ZM271.005 63.3181C274.594 72.2701 277.171 81.4446 278.511 90.5789C280.285 102.587 276.745 115.228 264.81 116.385C256.187 117.229 246.266 110.6 241.215 100.643C236.537 91.3999 236.885 79.7923 242.093 72.492C248.568 63.3687 258.095 60.3072 270.991 63.3049L271.005 63.3181ZM187.179 -20.2499C188.035 -16.4683 188.611 -12.7086 188.885 -9.03524C189.584 0.30813 188.764 16.88 175.495 22.9738C167.805 26.4908 157.072 20.2563 150.339 12.9643C140.361 2.1451 141.6 -9.74004 147.265 -16.7829C154.778 -26.1023 167.478 -27.2223 187.188 -20.2624L187.179 -20.2499Z"
                        fill="#4D55F2"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_11404_191009">
                        <rect width="181" height="104" rx="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                {/* Balance Text & Amount */}
                <div
                  className="flex flex-col items-start flex-shrink-0"
                  style={{ minWidth: 220 }}
                >
                  <span
                    className="text-[#666] text-[16px] font-normal leading-[1.35] tracking-[-0.03em] mb-1"
                    style={{ fontFamily: "Rethink Sans" }}
                  >
                    Available Balance
                  </span>
                  <span
                    className="text-[#000] text-[52px] font-semibold leading-[1.23] tracking-[-0.03em] mb-0"
                    style={{ fontFamily: "Rethink Sans" }}
                  >
                    {wallet
                      ? formatCurrency(Number(wallet.available_balance))
                      : formatCurrency(0)}
                    <span className="text-[32px] align-normal font-normal">
                      .00
                    </span>
                  </span>
                </div>
                {/* Actions */}
                <div className="flex flex-row gap-8 items-end ml-auto">
                  {/* Top-up */}
                  <div
                    onClick={handleTopup}
                    className="flex flex-col items-center gap-2 relative cursor-pointer"
                  >
                    <div className="w-[56px] h-[56px] rounded-[16px] bg-[#F5F6FE] flex items-center justify-center mb-1 relative">
                      {isTopupLoading ? (
                        <ClipLoader size={24} color="#4D55F2" />
                      ) : (
                        <img
                          src="/assets/wallet/wallet-topup-icon.svg"
                          alt="Top-up"
                          className="w-[56px] h-[56px]"
                        />
                      )}
                    </div>
                    <span
                      className="text-[#000073] text-[14px] font-normal leading-[1.2]"
                      style={{ fontFamily: "Rethink Sans" }}
                    >
                      {isTopupLoading ? "Loading..." : "Top-up"}
                    </span>
                  </div>
                  {/* Withdraw */}
                  <div
                    onClick={() => navigate("/withdrawal/select-account")}
                    className="flex flex-col items-center gap-2 cursor-pointer"
                  >
                    <div className="w-[56px] h-[56px] rounded-[16px] bg-[#F5F6FE] flex items-center justify-center mb-1">
                      <img
                        src="/assets/wallet/wallet-withdraw-icon.svg"
                        alt="Withdraw"
                        className="w-[56px] h-[56px]"
                      />
                    </div>
                    <span
                      className="text-[#000073] text-[14px] font-normal leading-[1.2]"
                      style={{ fontFamily: "Rethink Sans" }}
                    >
                      Withdraw
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Wallet Tabs and Table Section */}
            <div className="w-full max-w-full sm:max-w-[800px] mx-auto mt-6 sm:mt-8">
              <div className="flex flex-col sm:flex-row items-center gap-2 mb-6 sm:mb-8">
                <div className="relative w-full sm:w-auto">
                  <button
                    className={`w-full sm:w-auto px-4 sm:px-6 py-2 rounded-full font-semibold text-sm transition-all ${
                      activeTab === "transactions"
                        ? "bg-[#4D55F2] text-white"
                        : "bg-transparent text-[#666]"
                    }`}
                    onClick={() => setActiveTab("transactions")}
                    style={{ fontFamily: "Rethink Sans" }}
                  >
                    Transactions
                  </button>
                  {activeTab === "transactions" && (
                    <span className="hidden sm:block absolute left-1/2 -bottom-2 transform -translate-x-1/2 h-1.5 w-1.5 rounded-full bg-[#FF5519]" />
                  )}
                </div>
                <div className="relative w-full sm:w-auto">
                  <button
                    className={`w-full sm:w-auto px-4 sm:px-6 py-2 rounded-full font-semibold text-sm transition-all ${
                      activeTab === "withdrawal"
                        ? "bg-[#4D55F2] text-white"
                        : "bg-transparent text-[#666]"
                    }`}
                    onClick={() => setActiveTab("withdrawal")}
                    style={{ fontFamily: "Rethink Sans" }}
                  >
                    Withdrawal Accounts
                  </button>
                  {activeTab === "withdrawal" && (
                    <span className="hidden sm:block absolute left-1/2 -bottom-2 transform -translate-x-1/2 h-1.5 w-1.5 rounded-full bg-[#FF5519]" />
                  )}
                </div>
              </div>
              {activeTab === "transactions" && (
                <Card className="bg-white rounded-2xl shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] border-0 w-full">
                  {transactionState.loading ? (
                    <div className="flex flex-col items-center gap-10 py-[44px]">
                      <ClipLoader size={60} color="#4D55F2" />
                      <p className="text-[16px] text-[#808080]">
                        Loading transactions...
                      </p>
                    </div>
                  ) : !walletState.hasTransactions &&
                    !transactionFilters.search &&
                    !transactionFilters.from &&
                    !transactionFilters.to ? (
                    <EmptyTransactions />
                  ) : (
                    <>
                      <h1 className="text-[16px] sm:text-[18px] p-4 pt-5 font-medium tracking-tight">
                        Transactions
                      </h1>
                      {/* Search and Filter Controls */}
                      <div className="p-2 sm:p-4 space-y-4">
                        {/* Search and Filter Button Row */}
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-2">
                          <div className="relative w-full sm:w-auto mb-2 sm:mb-0">
                            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                              <svg
                                className="h-4 w-4 text-gray-400"
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M9.58332 17.4998C13.9556 17.4998 17.5 13.9554 17.5 9.58317C17.5 5.21092 13.9556 1.6665 9.58332 1.6665C5.21107 1.6665 1.66666 5.21092 1.66666 9.58317C1.66666 13.9554 5.21107 17.4998 9.58332 17.4998Z"
                                  stroke="#B3B3B3"
                                  strokeWidth="1.2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M18.3333 18.3332L16.6667 16.6665"
                                  stroke="#B3B3B3"
                                  strokeWidth="1.2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                            <input
                              type="text"
                              placeholder="Search Transactions"
                              value={transactionFilters.search}
                              onChange={(e) => {
                                const newFilters = {
                                  ...transactionFilters,
                                  search: e.target.value,
                                  page: 1,
                                };
                                setTransactionFilters(newFilters);
                                fetchTransactions(newFilters);
                              }}
                              className="pl-10 pr-4 py-2 italic tracking-[-1%] w-full sm:w-[500px] rounded-full text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                            />
                          </div>
                          <button
                            onClick={() => setShowDateFilters(!showDateFilters)}
                            className={`p-2 flex w-9 h-9 justify-center items-center rounded-full cursor-pointer transition-colors ${
                              showDateFilters
                                ? "bg-[#4D55F2] hover:bg-[#3d45d2]"
                                : "bg-[#F5F6FE] hover:bg-[#e1e5fc]"
                            }`}
                          >
                            <svg
                              className="w-[20px] h-[20px]"
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                opacity="0.4"
                                d="M17.1664 3.41667V5.25C17.1664 5.91667 16.7497 6.75 16.333 7.16667L12.7497 10.3333C12.2497 10.75 11.9164 11.5833 11.9164 12.25V15.8333C11.9164 16.3333 11.583 17 11.1664 17.25L9.99972 18C8.91638 18.6667 7.41638 17.9167 7.41638 16.5833V12.1667C7.41638 11.5833 7.08305 10.8333 6.74972 10.4167L5.91638 9.54167L10.7664 1.75H15.4997C16.4164 1.75 17.1664 2.5 17.1664 3.41667Z"
                                fill={showDateFilters ? "#FFFFFF" : "#4D55F2"}
                              />
                              <path
                                d="M9.41695 1.75L5.10028 8.675L3.58362 7.08333C3.16695 6.66667 2.83362 5.91667 2.83362 5.41667V3.5C2.83362 2.5 3.58362 1.75 4.50028 1.75H9.41695Z"
                                fill={showDateFilters ? "#FFFFFF" : "#4D55F2"}
                              />
                            </svg>
                          </button>
                        </div>

                        {/* Date Filter Row - Only show when toggled */}
                        {showDateFilters && (
                          <div className="flex flex-col sm:flex-row items-center gap-4">
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium text-[#535862]">
                                From:
                              </label>
                              <input
                                type="date"
                                value={
                                  transactionFilters.from
                                    ? transactionFilters.from.split("T")[0]
                                    : ""
                                }
                                onChange={(e) => {
                                  const newFilters = {
                                    ...transactionFilters,
                                    from: e.target.value
                                      ? `${e.target.value}T00:00:00Z`
                                      : "",
                                    page: 1,
                                  };
                                  setTransactionFilters(newFilters);
                                  fetchTransactions(newFilters);
                                }}
                                className="px-3 py-1 border border-[#e6e6e6] rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium text-[#535862]">
                                To:
                              </label>
                              <input
                                type="date"
                                value={
                                  transactionFilters.to
                                    ? transactionFilters.to.split("T")[0]
                                    : ""
                                }
                                onChange={(e) => {
                                  const newFilters = {
                                    ...transactionFilters,
                                    to: e.target.value
                                      ? `${e.target.value}T23:59:59Z`
                                      : "",
                                    page: 1,
                                  };
                                  setTransactionFilters(newFilters);
                                  fetchTransactions(newFilters);
                                }}
                                className="px-3 py-1 border border-[#e6e6e6] rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <button
                              onClick={() => {
                                const newFilters = {
                                  ...transactionFilters,
                                  from: "",
                                  to: "",
                                  search: "",
                                  page: 1,
                                };
                                setTransactionFilters(newFilters);
                                fetchTransactions(newFilters);
                              }}
                              className="px-3 py-1 text-sm text-[#4D55F2] hover:bg-[#F5F6FE] rounded-lg transition-colors"
                            >
                              Clear Filters
                            </button>
                          </div>
                        )}
                      </div>
                      <div className="w-full overflow-x-auto pt-4 sm:pt-6">
                        <div className="min-w-[600px]">
                          <table
                            className="min-w-full text-left font-[Rethink Sans]"
                            aria-label="Wallet Transactions"
                          >
                            <thead className="bg-white text-[#535862]">
                              <tr className="text-[14px] font-medium">
                                <th className="font-medium px-[20px] py-[12px] w-[211px]">
                                  Description
                                </th>
                                <th className="font-medium px-[24px] py-[12px] w-[138px]">
                                  Type
                                </th>
                                <th className="font-medium px-[24px] py-[12px] w-[155px]">
                                  Status
                                </th>
                                <th className="font-medium px-[24px] py-[12px] w-[136px]">
                                  Amount
                                </th>
                                <th className="font-medium px-[16px] py-[12px] w-[146px]">
                                  Date
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white text-[#535862] text-[14px] divide-y divide-[#F0F0F0]">
                              {transactionState.transactions.length === 0 ? (
                                <tr>
                                  <td
                                    colSpan={5}
                                    className="px-4 py-12 text-center"
                                  >
                                    <div className="flex flex-col items-center gap-4">
                                      <div className="w-16 h-16 rounded-full bg-[#F5F6FE] flex items-center justify-center">
                                        <svg
                                          className="w-8 h-8 text-[#4D55F2]"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                          />
                                        </svg>
                                      </div>
                                      <div>
                                        <h3 className="text-[16px] font-medium text-[#090909] mb-1">
                                          No transactions found
                                        </h3>
                                        <p className="text-[14px] text-[#808080]">
                                          {transactionFilters.search ||
                                          transactionFilters.from ||
                                          transactionFilters.to
                                            ? "Try adjusting your search or filter criteria"
                                            : "Your transactions will appear here"}
                                        </p>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              ) : (
                                transactionState.transactions.map(
                                  (transaction) => (
                                    <tr
                                      key={transaction.id}
                                      className="font-normal"
                                    >
                                      <td className="px-[20px] py-[16px] w-[211px]">
                                        {transaction.description}
                                      </td>
                                      <td className="px-[24px] py-[16px] w-[138px]">
                                        <span
                                          className={`px-2 py-1 rounded-full text-xs ${
                                            transaction.type === "credit"
                                              ? "bg-green-100 text-green-800"
                                              : "bg-red-100 text-red-800"
                                          }`}
                                        >
                                          {transaction.type}
                                        </span>
                                      </td>
                                      <td className="px-[24px] py-[16px] w-[155px]">
                                        <span
                                          className={`px-2 py-1 rounded-full text-xs ${
                                            transaction.status === "success"
                                              ? "bg-green-100 text-green-800"
                                              : transaction.status === "pending"
                                              ? "bg-yellow-100 text-yellow-800"
                                              : "bg-red-100 text-red-800"
                                          }`}
                                        >
                                          {transaction.status}
                                        </span>
                                      </td>
                                      <td className="px-[24px] py-[16px] w-[136px]">
                                        {formatCurrency(
                                          Number(transaction.amount)
                                        )}
                                      </td>
                                      <td className="px-[16px] py-[16px] w-[146px]">
                                        {new Date(
                                          transaction.created_at
                                        ).toLocaleDateString("en-NG", {
                                          day: "numeric",
                                          month: "long",
                                          year: "numeric",
                                        })}
                                      </td>
                                    </tr>
                                  )
                                )
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Pagination Controls */}
                      {transactionState.meta && (
                        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 border-t border-[#f0f0f0]">
                          <div className="text-sm text-[#535862]">
                            Showing{" "}
                            {(transactionState.meta.page - 1) *
                              transactionState.meta.per_page +
                              1}{" "}
                            to{" "}
                            {Math.min(
                              transactionState.meta.page *
                                transactionState.meta.per_page,
                              transactionState.meta.total
                            )}{" "}
                            of {transactionState.meta.total} transactions
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => {
                                if (transactionState.meta?.previous_page) {
                                  const newFilters = {
                                    ...transactionFilters,
                                    page: transactionFilters.page - 1,
                                  };
                                  setTransactionFilters(newFilters);
                                  fetchTransactions(newFilters);
                                }
                              }}
                              disabled={!transactionState.meta?.previous_page}
                              className="px-3 py-1 text-sm border border-[#e6e6e6] rounded-lg hover:bg-[#f5f5f5] disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Previous
                            </button>
                            <span className="px-3 py-1 text-sm">
                              Page {transactionState.meta.page} of{" "}
                              {transactionState.meta.page_count}
                            </span>
                            <button
                              onClick={() => {
                                if (transactionState.meta?.next_page) {
                                  const newFilters = {
                                    ...transactionFilters,
                                    page: transactionFilters.page + 1,
                                  };
                                  setTransactionFilters(newFilters);
                                  fetchTransactions(newFilters);
                                }
                              }}
                              disabled={!transactionState.meta?.next_page}
                              className="px-3 py-1 text-sm border border-[#e6e6e6] rounded-lg hover:bg-[#f5f5f5] disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              Next
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </Card>
              )}
              {activeTab === "withdrawal" && (
                <Card className="bg-white rounded-2xl shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] border-0 w-full">
                  {payoutAccountState.loading ? (
                    <div className="flex flex-col items-center gap-10 py-[44px]">
                      <ClipLoader size={60} color="#4D55F2" />
                      <p className="text-[16px] text-[#808080]">
                        Loading accounts...
                      </p>
                    </div>
                  ) : !walletState.hasWithdrawalAccounts ? (
                    <EmptyWithdrawalAccounts />
                  ) : (
                    <div className="w-full max-w-full sm:max-w-[763px] mx-auto rounded-2xl p-4 sm:p-6 flex flex-col gap-4 sm:gap-5">
                      <div className="flex flex-col sm:flex-row items-start sm:items-end justify-between gap-2 sm:gap-4">
                        <h1
                          className="text-[16px] sm:text-[18px] font-medium text-[#090909] tracking-tight"
                          style={{ fontFamily: "Rethink Sans" }}
                        >
                          Withdrawal Accounts
                        </h1>
                        <button
                          className="w-fit sm:w-auto flex items-center bg-[#4D55F2] text-white rounded-full shadow px-4 py-2 font-semibold text-sm gap-2 border border-[#4D55F2]"
                          style={{ borderRadius: 64 }}
                          onClick={() => setShowWithdrawalModal(true)}
                        >
                          Add New
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              opacity="0.4"
                              d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                              fill="white"
                            />
                            <path
                              d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                              fill="white"
                            />
                          </svg>
                        </button>
                      </div>
                      <div className="flex flex-col gap-3 mt-2">
                        {payoutAccountState.accounts.map((account) => (
                          <div
                            key={account.id}
                            className="flex flex-row justify-between items-start sm:items-end bg-white rounded-[10px] shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] px-4 sm:px-6 py-4 gap-4 sm:gap-[152px]"
                          >
                            <div>
                              <div
                                className="font-semibold text-[#00008C] text-[16px]"
                                style={{ fontFamily: "Rethink Sans" }}
                              >
                                {account.account_name}
                              </div>
                              <div className="mt-2">
                                <div
                                  className="text-[10px] text-[#B3B3B3] font-normal tracking-[-0.02em]"
                                  style={{ fontFamily: "Rethink Sans" }}
                                >
                                  Account number
                                </div>
                                <div
                                  className="text-[14px] text-[#000] font-normal"
                                  style={{ fontFamily: "Rethink Sans" }}
                                >
                                  {account.account_number}
                                </div>
                              </div>
                            </div>
                            <div className="flex flex-col items-end">
                              <div className="flex flex-col items-end justify-end gap-2">
                                {/* Bank icon placeholder - you can add bank-specific icons based on bank_code */}
                                <div className="w-8 h-8 bg-[#D94F00] rounded flex items-center justify-center">
                                  <span className="text-white text-xs font-bold">
                                    Bank
                                  </span>
                                </div>
                                <div>
                                  <div
                                    className="text-[10px] text-[#B3B3B3] font-normal"
                                    style={{ fontFamily: "Rethink Sans" }}
                                  >
                                    Bank name
                                  </div>
                                  <div
                                    className="text-[14px] text-[#000] font-normal"
                                    style={{ fontFamily: "Rethink Sans" }}
                                  >
                                    {(() => {
                                      const bank =
                                        payoutAccountState.banks.find(
                                          (bank) =>
                                            bank.bank_code === account.bank_code
                                        );
                                      return bank
                                        ? bank.bank_name
                                        : account.bank_code;
                                    })()}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </Card>
              )}
            </div>
          </>
        )}
      </div>

      {showWithdrawalModal && (
        <WithdrawalAccountSetupModal
          isOpen={showWithdrawalModal}
          onClose={() => setShowWithdrawalModal(false)}
          onAccountAdded={() => {
            fetchPayoutAccounts(); // Refresh the accounts list
            setWalletState((prev) => ({
              ...prev,
              hasWithdrawalAccounts: true,
            }));
          }}
          banks={payoutAccountState.banks}
        />
      )}
      {showSecureWalletModal && (
        <SecureWalletModal
          isOpen={showSecureWalletModal}
          onClose={() => setShowSecureWalletModal(false)}
          onCreateWallet={() => {
            setShowSecureWalletModal(false);
            setShowWalletSetupModal(true);
          }}
        />
      )}
      {showWalletSetupModal && (
        <WalletSetupModal
          isOpen={showWalletSetupModal}
          onClose={() => setShowWalletSetupModal(false)}
          setShowSetPinModal={setShowSetPinModal}
          onWalletCreated={(walletObj) => {
            setCreatedWalletId(walletObj.id);
            setWallet(walletObj);
            setWalletState((prev) => ({ ...prev, hasWallet: true }));
          }}
        />
      )}
      {showSetPinModal && (
        <SetTransactionPinModal
          isOpen={showSetPinModal}
          onClose={() => setShowSetPinModal(false)}
          onSuccess={() => {
            setShowSetPinModal(false);
            setShowPinSuccessModal(true);
          }}
          walletId={createdWalletId}
          onPinSet={refreshUserData}
        />
      )}
      {showPinSuccessModal && (
        <PinSuccessModal
          isOpen={showPinSuccessModal}
          onClose={() => {
            setShowPinSuccessModal(false);
            // Re-fetch wallet to get updated pin_set
            WalletAPI.getUserWallets().then((wallets) => {
              if (wallets && wallets.length > 0) {
                setWallet(wallets[0]);
                setWalletState((prev) => ({ ...prev, hasWallet: true }));
              }
            });
          }}
        />
      )}
    </div>
  );
};

export default WalletPage;
