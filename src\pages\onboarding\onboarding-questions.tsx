import { useState } from 'react';
import { OnboardingLayout } from '../../layouts/onboarding-ques';
import { EventCategory } from './even-category';
import { EventDetailsForm } from './event-details-form';
import { EventToolsSelection } from './event-tools-select';
// import { InviteCollaborators } from './invite-collaborators';
import { CreatedEventData } from '../../lib/store/event';

interface StepData {
  category?: string;
  eventDetails?: {
    name: string;
    description: string;
    startDate?: string;
    startTime?: string;
    endDate?: string;
    endTime?: string;
    location: string;
    imageUrl?: string;
    imageFile?: File;
  };
  createdEventData?: CreatedEventData; // API response data from event creation
  tools?: string[];
  collaborators?: string[];
}

export const OnboardingQuestions = () => {
  const [activeStep, setActiveStep] = useState(3);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [stepData, setStepData] = useState<StepData>({});
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');

  const handleStepChange = (step: number) => {
    if (
      completedSteps.includes(step) ||
      step === activeStep ||
      step === activeStep - 1
    ) {
      setDirection(step < activeStep ? 'backward' : 'forward');
      setActiveStep(step);
    }
  };

  const handleNext = () => {
    setDirection('forward');
    setCompletedSteps((prev) => [...new Set([...prev, activeStep])]);
    setActiveStep((prev) => prev + 1);
  };

  const handleCategoryComplete = (categoryId: string) => {
    setStepData((prev) => ({ ...prev, category: categoryId }));
    handleNext();
  };

  const handleEventDetailsComplete = (
    details: {
      name: string;
      description: string;
      startDate: string;
      startTime: string;
      endDate: string;
      endTime: string;
      location: string;
      image?: File;
      imageUrl?: string;
    },
    createdEventData?: CreatedEventData
  ) => {
    setStepData((prev) => ({
      ...prev,
      eventDetails: {
        name: details.name,
        startDate: details.startDate,
        startTime: details.startTime,
        endDate: details.endDate,
        endTime: details.endTime,
        location: details.location,
        description: details.description,
        imageFile: details.image,
        imageUrl: details.imageUrl,
      },
      createdEventData: createdEventData,
    }));
    handleNext();
  };

  const handleToolsComplete = (tools: string[]) => {
    setStepData((prev) => ({ ...prev, tools }));
    handleNext();
  };

  // const handleCollaboratorsComplete = (collaborators: string[]) => {
  //   setStepData((prev) => ({ ...prev, collaborators }));
  // };

  return (
    <div className="flex overflow-hidden flex-col min-h-screen pb-52 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
      <div
        className="hidden md:block absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat z-0 bg-contain"
        style={{
          backgroundSize: '100% auto',
        }}
      />
      <OnboardingLayout
        activeStep={activeStep}
        completedSteps={completedSteps}
        onStepChange={handleStepChange}
      />
      <div className="max-w-[550px] mt-12 w-full mx-auto z-50 px-4 md:px-0">
        {activeStep === 1 && (
          <EventCategory
            onNext={handleCategoryComplete}
            initialData={stepData.category}
          />
        )}
        {activeStep === 2 && (
          <EventDetailsForm
            onNext={handleEventDetailsComplete}
            initialData={stepData.eventDetails}
            direction={direction}
            categoryId={stepData.category}
          />
        )}
        {activeStep === 3 && (
          <EventToolsSelection
            onNext={handleToolsComplete}
            initialData={stepData.tools}
            direction={direction}
          />
        )}
        {/* {activeStep === 3 && (
          <EventToolsSelection
            onNext={handleToolsComplete}
            initialData={stepData.tools}
            direction={direction}
          />
        )}
        {activeStep === 4 && (
          <InviteCollaborators
            onNext={handleCollaboratorsComplete}
            initialData={stepData.collaborators}
            eventName={stepData.eventDetails?.name}
          />
        )} */}
      </div>
    </div>
  );
};
