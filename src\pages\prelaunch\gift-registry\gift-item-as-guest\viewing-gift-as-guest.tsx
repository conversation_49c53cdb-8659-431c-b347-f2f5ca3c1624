import {
  Info<PERSON>ircle,
  Tag2,
  Tick<PERSON>ircle,
  ArrowRight2,
  Location,
  Copy,
  CloseCircle,
  ArrowCircleRight,
} from "iconsax-react";
import giftIcon from "../../../../assets/images/gift-blur.png";
import { Button } from "../../../../components/button/onboardingButton";
import { useState, useRef, useEffect } from "react";
import iphone from "../../../../assets/images/iphone.svg";
import cash from "../../../../assets/images/cash-img.png";
import { Footer } from "../../footer";
import { useNavigate, useParams } from "react-router-dom";
import {
  GuestGiftsAPI,
  CashGift,
  ItemGift,
  GiftReservation,
} from "../../../../lib/apis/guestGiftsApi";
import { toast } from "react-toastify";

export const ViewingGiftAsGuest = () => {
  const { eventId, giftId } = useParams<{ eventId: string; giftId?: string }>();
  console.log(giftId);
  const navigate = useNavigate();

  const [activeTab, setActiveTab] = useState<"items" | "cash">("items");
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [showReturningGuestModal, setShowReturningGuestModal] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [returningGuestEmail, setReturningGuestEmail] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", ""]);
  const [emailError, setEmailError] = useState("");

  // API state
  const [cashGifts, setCashGifts] = useState<CashGift[]>([]);
  const [itemGifts, setItemGifts] = useState<ItemGift[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reservation state
  const [reservations, setReservations] = useState<GiftReservation[]>([]);
  const [showReservations, setShowReservations] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isInitiatingAccess, setIsInitiatingAccess] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  console.log(accessToken);

  const otpRefs = [
    useRef(null),
    useRef(null),
    useRef(null),
    useRef(null),
    useRef(null),
  ];

  // Dummy delivery details (replace with real data as needed)
  const deliveryPhone = "+234 (*************";
  const deliveryAddress = "Lagos, Island, Lagos Nigeria";

  // Copy to clipboard handler
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Fetch gifts data
  useEffect(() => {
    if (!eventId) return;

    const fetchGifts = async () => {
      setLoading(true);
      setError(null);

      try {
        const [cashResponse, itemResponse] = await Promise.all([
          GuestGiftsAPI.getCashGifts(eventId),
          GuestGiftsAPI.getItemGifts(eventId),
        ]);

        setCashGifts(cashResponse.gifts);
        setItemGifts(itemResponse.gifts);
      } catch (err) {
        const errorMsg =
          typeof err === "object" && err && "message" in err
            ? (err as { message?: string }).message ?? "Failed to fetch gifts"
            : "Failed to fetch gifts";
        setError(errorMsg);
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchGifts();
  }, [eventId]);

  // Transform API data to component format
  const giftItems = itemGifts.map((gift) => ({
    id: gift.id,
    name: gift.name,
    price: parseFloat(gift.price),
    image: gift.image_preview_url || iphone,
    reserved: gift.status === "reserved",
    purchased: gift.status === "purchased",
    mostWanted: false, // This would need to be determined by business logic
    description: gift.description,
    type: "standard",
    link: gift.link,
    quantity: gift.quantity,
    crowdGiftingEnabled: false, // Add this for now, would need business logic
    contributedAmount: 0, // Add this for now
    contributorCount: 0, // Add this for now
  }));
  const cashItems = cashGifts.map((gift) => ({
    id: gift.id,
    amount: parseFloat(gift.amount),
    description: gift.description,
    received: 0, // This would need to be calculated from reservations
    status: gift.status,
    is_crowd_gift: gift.is_crowd_gift,
    currency: gift.currency,
  }));
  const handleMakePayment = () => {
    navigate("/purchasing-gift-items");
  };

  // Email validation
  const validateEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  // Handle Continue in Returning Guest modal
  const handleReturningGuestContinue = async () => {
    if (!validateEmail(returningGuestEmail)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    setEmailError("");
    setIsInitiatingAccess(true);

    try {
      await GuestGiftsAPI.initiateAccess({ email: returningGuestEmail });
      setShowReturningGuestModal(false);
      setShowOtpModal(true);
      toast.success("OTP sent to your email");
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Failed to send OTP"
          : "Failed to send OTP";
      setEmailError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsInitiatingAccess(false);
    }
  };

  // Handle OTP input
  const handleOtpChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newOtp = [...otp];
    newOtp[idx] = value;
    setOtp(newOtp);
    if (value && idx < 4) {
      (otpRefs[idx + 1].current as unknown as HTMLInputElement)?.focus();
    }
    if (!value && idx > 0) {
      (otpRefs[idx - 1].current as unknown as HTMLInputElement)?.focus();
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async () => {
    const otpCode = otp.join("");
    if (otpCode.length !== 5) {
      toast.error("Please enter the complete OTP");
      return;
    }

    setIsVerifyingOtp(true);
    try {
      const response = await GuestGiftsAPI.verifyOTP({
        email: returningGuestEmail,
        otp: otpCode,
      });

      setAccessToken(response.access_token);
      setShowOtpModal(false);
      toast.success("Access verified successfully!");

      // Fetch reservations
      await fetchReservations(response.access_token);
      setShowReservations(true);
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Invalid OTP"
          : "Invalid OTP";
      toast.error(errorMsg);
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Fetch reservations
  const fetchReservations = async (token?: string) => {
    console.log(token);
    try {
      // Note: You might need to modify the API to accept the access token
      const response = await GuestGiftsAPI.getReservations();
      setReservations(response.reservations);
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to fetch reservations"
          : "Failed to fetch reservations";
      toast.error(errorMsg);
    }
  };

  // Handle Confirm OTP
  const handleConfirmOtp = () => {
    handleOtpVerification();
  };

  return (
    <div className="flex pt-16 sm:pt-20 min-h-screen flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] relative">
      <div
        className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
        style={{
          backgroundSize: "100% auto",
        }}
      />

      <div className="mx-auto w-full z-20 px-4 sm:px-0">
        {/* Tabs and View Delivery Details Button */}

        {/* Modal for Delivery Details */}
        {showDeliveryModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 bg-[#EDEEFE] rounded-full p-2 hover:bg-[#d6d8f5] transition-colors"
                onClick={() => setShowDeliveryModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              {/* Title & Subtitle */}
              <div className="mb-4">
                <h2 className="font-bold text-[22px] text-[#090909] mb-1">
                  Delivery Details
                </h2>
                <p className="font-normal text-[16px] text-[#8E8E93]">
                  Easily copy the full delivery address and phone number to your
                  clipboard in one tap
                </p>
              </div>
              {/* Phone Number Field */}
              <div className="mb-4">
                <label className="block font-bold text-[14px] text-[#414651] mb-1">
                  Delivery Phone Number
                </label>
                <div className="flex items-center bg-white border border-[#D5D7DA] rounded-[63px] px-4 py-2">
                  <span className="text-[16px] font-bold text-black flex-1">
                    {deliveryPhone}
                  </span>
                  <button
                    className="flex items-center gap-1 bg-[#F5F9FF] text-[#5F66F3] font-semibold text-[14px] rounded-[16px] px-3 py-1 ml-2 hover:bg-[#e0e7ff] transition-colors"
                    onClick={() => handleCopy(deliveryPhone)}
                  >
                    <Copy size={16} color="#4D55F2" variant="Bulk" /> Copy
                  </button>
                </div>
              </div>
              {/* Address Field */}
              <div className="mb-2">
                <label className="block font-bold text-[14px] text-[#414651] mb-1">
                  Full Address
                </label>
                <div className="flex items-center bg-white border border-[#D5D7DA] rounded-[64px] px-4 py-2">
                  <Location
                    size={20}
                    color="#292D32"
                    variant="Bulk"
                    className="mr-2"
                  />
                  <span className="text-[16px] font-bold text-black flex-1">
                    {deliveryAddress}
                  </span>
                  <button
                    className="flex items-center gap-1 bg-[#F5F9FF] text-[#5F66F3] font-semibold text-[14px] rounded-[16px] px-3 py-1 ml-2 hover:bg-[#e0e7ff] transition-colors"
                    onClick={() => handleCopy(deliveryAddress)}
                  >
                    <Copy size={16} color="#4D55F2" variant="Bulk" /> Copy
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Returning Guest Email Modal */}
        {showReturningGuestModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4  rounded-full p-2  transition-colors"
                onClick={() => setShowReturningGuestModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              <div className="mb-6 mt-2">
                <div className="text-[28px] font-bold text-[#000] mb-1">
                  Enter your Email
                </div>
                <div className="text-[#8E8E93] text-[16px] font-normal mb-2">
                  Please enter your email address to continue
                </div>

                <label className="block font-medium text-[14px] text-[#414651] mb-1 mt-7">
                  Email Address
                </label>
                <input
                  type="email"
                  className="w-full border border-[#D5D7DA] rounded-[64px] px-4 py-3 text-[16px] font-normal text-black focus:outline-none focus:border-[#4D55F2] mb-2"
                  placeholder="<EMAIL>"
                  value={returningGuestEmail}
                  onChange={(e) => setReturningGuestEmail(e.target.value)}
                  style={{ fontFamily: "Rethink Sans" }}
                />
                {emailError && (
                  <div className="text-red-500 text-xs mb-2">{emailError}</div>
                )}
                <button
                  className=" w-full mt-10 flex items-center justify-center gap-2 bg-[#4D55F2]  text-white font-bold text-[16px] rounded-[38px] px-4 py-3 shadow-sm hover:bg-[#3a5a8c] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ fontFamily: "Rethink Sans" }}
                  onClick={handleReturningGuestContinue}
                  disabled={isInitiatingAccess}
                >
                  {isInitiatingAccess ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Sending OTP...
                    </>
                  ) : (
                    <>
                      Continue
                      <ArrowRight2 size={20} color="#fff" variant="Bulk" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* OTP Modal */}
        {showOtpModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
            <div
              className="relative bg-white rounded-[16px] shadow-2xl p-8 w-full max-w-[480px]"
              style={{ fontFamily: "Rethink Sans" }}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 bg-[#EDEEFE] rounded-full p-2 hover:bg-[#d6d8f5] transition-colors"
                onClick={() => setShowOtpModal(false)}
                aria-label="Close"
              >
                <CloseCircle size={24} color="#4D55F2" variant="Bulk" />
              </button>
              <div className="mb-6 mt-2">
                <div className="text-[28px] font-bold text-[#000] mb-2">
                  Enter OTP
                </div>
                <div className="text-[#8E8E93] text-[16px] font-normal mb-14">
                  Check your mail for a 5-Digit OTP sent to{" "}
                  <span className="font-bold text-black">
                    {returningGuestEmail}
                  </span>{" "}
                  and input in the field below
                </div>

                <div className="flex gap-3 justify-center mb-14">
                  {otp.map((digit, idx) => (
                    <input
                      key={idx}
                      ref={otpRefs[idx]}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      className={`w-14 h-14 text-center text-[48px] font-medium rounded-[50%] border ${
                        digit
                          ? "border-[#4D55F2] shadow-[0px_0px_10px_0px_rgba(77,85,242,0.25)]"
                          : "  border-[#DBDDFC]"
                      } focus:border-[#4D55F2] focus:outline-none`}
                      value={digit}
                      onChange={(e) => handleOtpChange(idx, e.target.value)}
                      onFocus={(e) => e.target.select()}
                      style={{
                        fontFamily: "Rethink Sans",
                        color: digit ? "#00000D" : "#4D55F2",
                      }}
                    />
                  ))}
                </div>
                <button
                  className="mt-10 w-full flex items-center justify-center gap-2 bg-[#4D55F2] text-white font-bold text-[16px] rounded-[40px] px-4 py-3 shadow-sm hover:bg-[#3a5a8c] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ fontFamily: "Rethink Sans" }}
                  onClick={handleConfirmOtp}
                  disabled={isVerifyingOtp}
                >
                  {isVerifyingOtp ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      Confirm OTP
                      <ArrowRight2 size={20} color="#fff" variant="Bulk" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="text-center">
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src={giftIcon}
              alt="Gift"
              className="w-12 h-12 sm:w-16 sm:h-16"
            />
          </div>

          <div className="text-sm sm:text-base uppercase text-[#333333] tracking-widest mb-2 sm:mb-3 px-2">
            CURATED BY OLADELE JOHNSON
          </div>

          <h1 className="text-2xl sm:text-3xl md:text-[48px] font-bold text-[#000059] mb-2 sm:mb-3 px-2 leading-tight sm:leading-normal">
            Oladele's birthday gifts
          </h1>

          <p className="text-[#666666] leading-[150%] text-base sm:text-lg mb-3 px-4 sm:px-0">
            Celebrate with me! Pick a gift and{" "}
            <br className="hidden sm:block" />
            make my day special. 💙{" "}
          </p>

          <Button
            variant="primary"
            size="sm"
            iconLeft={<InfoCircle size="14" color="#FF6630" variant="Bulk" />}
            className="bg-[#FDEFE9] text-cus-orange mx-auto underline italic font-bold text-sm sm:text-base"
          >
            How to Get Gift
          </Button>

          {/* Returning Guest Bar */}
          <div className="flex items-center mx-auto w-fit mt-10 sm:mt-10 justify-between bg-[#EBF3FE] rounded-[12px] px-4 py-2  mb-6">
            <span className="font-bold text-[16px] text-[#322D66] italic font-[Rethink Sans]">
              Are you a Returning Guest? Click here to continue
            </span>
            <button
              className="flex items-center gap-1 ml-5 bg-[#5075AF] text-white font-semibold text-xs rounded-full italic px-4 py-1.5 shadow-sm hover:bg-[#3a5a8c] transition-colors"
              style={{ fontFamily: "Rethink Sans" }}
              onClick={() => setShowReturningGuestModal(true)}
            >
              Continue
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                  fill="white"
                />
                <path
                  d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                  fill="white"
                />
              </svg>
            </button>
          </div>

          <div className="w-[560px] mx-auto justify-between items-center flex">
            <div className="flex  justify-between w-[560px]">
              <div className="relative flex justify-center ">
                <div className="flex w-fit">
                  <button
                    className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                      activeTab === "items"
                        ? "bg-[#4D55F2] text-white"
                        : "text-gray-500"
                    }`}
                    onClick={() => setActiveTab("items")}
                  >
                    Gift Items
                  </button>
                  <button
                    className={`px-4 sm:px-5 py-2.5 sm:py-2 rounded-full text-sm font-medium transition-colors min-w-[100px] sm:min-w-0 ${
                      activeTab === "cash"
                        ? "bg-[#4D55F2] text-white"
                        : "text-gray-500"
                    }`}
                    onClick={() => setActiveTab("cash")}
                  >
                    Cashgifts
                  </button>
                </div>

                <div
                  className={`absolute bottom-[-14px] w-1.5 h-1.5 bg-[#FF6630] rounded-full transition-all duration-300 ${
                    activeTab === "items"
                      ? "left-[calc(50%-50px)]"
                      : "left-[calc(50%+50px)]"
                  }`}
                />
              </div>
              <button
                className="flex items-center gap-2 bg-[#F5F6FE] border border-[#F0F0F0] rounded-full px-4 py-2 h-[40px] text-[#343CD8] font-medium text-xs  hover:bg-[#edeefe] transition-colors"
                style={{ fontFamily: "Rethink Sans" }}
                onClick={() => setShowDeliveryModal(true)}
              >
                View Delivery Details
                <ArrowCircleRight size={20} color="#343CD8" variant="Bulk" />
              </button>
            </div>
          </div>

          <div className="mt-8 sm:mt-10 mb-16 sm:mb-20 max-w-[560px] w-full mx-auto">
            {loading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4D55F2]"></div>
                <p className="mt-4 text-gray-600">Loading gifts...</p>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="text-red-500 text-center">
                  <p className="text-lg font-medium mb-2">
                    Error loading gifts
                  </p>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
            ) : activeTab === "items" ? (
              <div>
                <div className="space-y-3 sm:space-y-4">
                  {giftItems.length === 0 ? (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No gift items available</p>
                    </div>
                  ) : (
                    giftItems.map((item) => (
                      <div
                        key={item.id}
                        className="bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl flex flex-col md:flex-row items-stretch border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] cursor-pointer hover:shadow-lg transition-shadow relative"
                      >
                        {item.mostWanted && (
                          <div className="absolute italic text-primary-750 top-2 right-2 bg-primary-150 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10">
                            📍MOST WANTED{" "}
                          </div>
                        )}
                        <div className="w-full md:w-[155px] h-[200px] sm:h-[180px] md:h-auto md:min-h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1 text-center md:text-left p-4 sm:p-5">
                          <h3 className="text-lg sm:text-xl md:text-[22px] font-medium text-grey-750 mb-2">
                            {item.name}
                          </h3>
                          <p className="text-sm sm:text-base text-grey-100 mb-4">
                            {item.description}
                          </p>

                          <div className="flex flex-col sm:flex-row gap-2 items-center mb-4">
                            <div className="flex items-center gap-2 bg-light-blue-150 text-orange-700 px-3 py-2 sm:px-2.5 sm:py-1.5 rounded-full">
                              <Tag2 size={12} variant="Bulk" color="#5925DC" />
                              <span className="text-primary text-sm font-semibold">
                                ₦{item.price.toLocaleString()}
                              </span>
                            </div>

                            {item.reserved && (
                              <div className="flex items-center gap-1 font-bold italic bg-grin text-grin-100 text-sm px-3 py-2 sm:px-2.5 sm:py-1.5 rounded-full">
                                <svg
                                  width="14"
                                  height="14"
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <rect
                                    x="1"
                                    y="1"
                                    width="12"
                                    height="12"
                                    rx="6"
                                    fill="#3CC35C"
                                  />
                                  <rect
                                    x="1"
                                    y="1"
                                    width="12"
                                    height="12"
                                    rx="6"
                                    stroke="#3CC35C"
                                    strokeWidth="2"
                                  />
                                  <path
                                    d="M9.18395 5.36426L6.18395 8.36426L4.82031 7.00062"
                                    stroke="white"
                                    strokeWidth="1.4"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <span>Reserved by you</span>
                              </div>
                            )}
                          </div>

                          {/* Crowd Gifting Section */}
                          {item.crowdGiftingEnabled && (
                            <div className="mb-4">
                              <div className="flex items-center gap-1 mb-2">
                                <TickCircle
                                  size={16}
                                  color="#3CC35C"
                                  variant="Bold"
                                  className="opacity-50"
                                />
                                <span className="text-sm font-medium text-grey-500">
                                  Crowd gifting enabled
                                </span>
                              </div>

                              {/* Progress Bar */}
                              <div className="w-full max-w-[226px] mx-auto md:mx-0 bg-gray-200 rounded-full h-2 mb-2">
                                <div
                                  className="bg-primary h-2 rounded-full transition-all duration-300"
                                  style={{
                                    width: `${
                                      (item.contributedAmount / item.price) *
                                      100
                                    }%`,
                                  }}
                                ></div>
                              </div>

                              <p className="text-sm text-grey-950 text-center md:text-left">
                                ₦{item.contributedAmount.toLocaleString()}{" "}
                                contributed by {item.contributorCount} people
                              </p>
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex justify-center md:justify-end">
                            {item.purchased ? (
                              <button className="border border-primary-110 italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0">
                                Complete Purchase
                              </button>
                            ) : (
                              item.reserved &&
                              !item.purchased && (
                                <button
                                  onClick={handleMakePayment}
                                  className="border border-primary-110 text-white bg-primary italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0"
                                >
                                  Make Payment
                                </button>
                              )
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            ) : (
              <div>
                <div className="space-y-3 sm:space-y-4">
                  {cashItems.length === 0 ? (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No cash gifts available</p>
                    </div>
                  ) : (
                    cashItems.map((item) => (
                      <div
                        key={item.id}
                        className="bg-white mx-0 sm:mx-4 md:mx-0 rounded-xl cursor-pointer hover:shadow-lg border border-[#F0F0F0] shadow-[0px_12px_120px_0px_rgba(95,95,95,0.06)] transition-shadow"
                      >
                        <div className="flex flex-col-reverse md:flex-row items-stretch gap-4 min-h-[120px]">
                          <div className="w-full md:w-[155px] h-[200px] sm:h-[180px] md:h-full bg-gray-200 md:rounded-l-lg rounded-t-xl md:rounded-t-none overflow-hidden flex-shrink-0">
                            <img
                              src={cash}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex flex-1 flex-col justify-between p-4 sm:p-5 md:py-4 md:pr-4">
                            <div className="text-center md:text-end mb-3 md:mb-0">
                              {item.status === "available" && (
                                <span className="text-xs font-bold italic text-primary-750 px-3 py-2 sm:px-2 sm:py-1 rounded-full bg-primary-150 uppercase tracking-wide">
                                  Available
                                </span>
                              )}
                              {item.status === "reserved" && (
                                <span className="text-xs font-bold italic text-green-600 px-3 py-2 sm:px-2 sm:py-1 rounded-full bg-grin uppercase tracking-wide">
                                  Reserved
                                </span>
                              )}
                              {item.status === "most_wanted" && (
                                <div className="flex justify-center md:justify-end items-center">
                                  <div className="bg-primary-150 rounded-full px-3 py-2 sm:px-2 sm:py-1 italic font-bold gap-1">
                                    <span>📍</span>
                                    <span className="text-xs font-medium text-primary-750 uppercase tracking-wide">
                                      Most Wanted
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex-1 text-center md:text-start mb-4 md:mb-0">
                              <h3 className="text-xl sm:text-2xl md:text-[28px] font-extrabold text-grey-750 mb-2">
                                ₦{item.amount.toLocaleString()}
                              </h3>
                              <p className="text-sm sm:text-base text-grey-100 max-w-full md:max-w-[201px]">
                                {item.description}
                              </p>
                            </div>
                            <div className="flex w-full justify-center md:justify-end">
                              <button className="border border-primary-110 italic font-semibold text-sm px-4 py-2.5 sm:px-2.5 sm:py-1.5 rounded-full min-w-[140px] sm:min-w-0">
                                Send Cashgift{" "}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Reservations Modal */}
      {showReservations && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-[#000]">
                  Your Reservations
                </h2>
                <button
                  onClick={() => setShowReservations(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <CloseCircle size={24} />
                </button>
              </div>

              {reservations.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-600">No reservations found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reservations.map((reservation) => (
                    <div
                      key={reservation.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-[#000]">
                            {reservation.gift_type === "cash"
                              ? "Cash Gift"
                              : "Item Gift"}
                          </p>
                          <p className="text-sm text-gray-600">
                            Order: {reservation.order_number}
                          </p>
                        </div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            reservation.status === "active"
                              ? "bg-green-100 text-green-800"
                              : reservation.status === "expired"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {reservation.status}
                        </span>
                      </div>
                      <p className="text-lg font-bold text-[#4D55F2]">
                        ₦{parseFloat(reservation.amount).toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        Expires:{" "}
                        {new Date(reservation.expires_at).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};
