import { EventParkAPI } from "../event-park-api";
import { isAxiosError } from "axios";

// Cash Gift Types
export interface CashGift {
  amount: string;
  created_at: string;
  currency: string;
  description: string;
  event_id: string;
  id: string;
  is_crowd_gift: boolean;
  status: string;
  updated_at: string;
}

// Item Gift Types
export interface ItemGift {
  created_at: string;
  currency_code: string;
  description: string;
  event_id: string;
  id: string;
  image_preview_key: string;
  image_preview_url: string;
  link: string;
  name: string;
  price: string;
  quantity: number;
  status: string;
  updated_at: string;
}

// Gift Reservation Types
export interface GiftReservation {
  amount: string;
  created_at: string;
  expires_at: string;
  gift_id: string;
  gift_type: string;
  gifter_email: string;
  gifter_first_name: string;
  gifter_id: string;
  gifter_last_name: string;
  id: string;
  order_number: string;
  status: string;
  updated_at: string;
}

// Meta information for pagination
export interface GiftMeta {
  from: string;
  next_page: boolean;
  page: number;
  page_count: number;
  per_page: number;
  previous_page: boolean;
  to: string;
  total: number;
}

// API Response Types
export interface CashGiftsResponse {
  gifts: CashGift[];
  meta: GiftMeta;
}

export interface ItemGiftsResponse {
  gifts: ItemGift[];
  meta: GiftMeta;
}

export interface ReservationsResponse {
  reservations: GiftReservation[];
  meta: GiftMeta;
}

// Query Parameters
export interface GiftQueryParams {
  from?: string;
  page?: number;
  per_page?: number;
  status?: string;
  to?: string;
}

export interface ReservationQueryParams extends GiftQueryParams {
  type?: string;
}

// Request Bodies
export interface CashReservationRequest {
  amount: string;
}

export interface OrderRequest {
  order_number: string;
}

export interface AccessInitiateRequest {
  email: string;
}

export interface OTPVerifyRequest {
  email: string;
  otp: string;
}

// Access Response Types
export interface AccessInitiateResponse {
  expires_at: string;
}

export interface OTPVerifyResponse {
  access_token: string;
  expires_at: string;
}

export const GuestGiftsAPI = {
  /**
   * Fetch cash gifts for an event as a guest
   */
  async getCashGifts(
    eventId: string,
    params: GiftQueryParams = {}
  ): Promise<CashGiftsResponse> {
    try {
      const response = await EventParkAPI().get<CashGiftsResponse>(
        `/v1/guest/events/${eventId}/gifts/cash`,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch cash gifts",
      };
    }
  },

  /**
   * Fetch item gifts for an event as a guest
   */
  async getItemGifts(
    eventId: string,
    params: GiftQueryParams = {}
  ): Promise<ItemGiftsResponse> {
    try {
      const response = await EventParkAPI().get<ItemGiftsResponse>(
        `/v1/guest/events/${eventId}/gifts/item`,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch item gifts",
      };
    }
  },

  /**
   * Create reservation for a cash gift
   */
  async reserveCashGift(
    giftId: string,
    request: CashReservationRequest
  ): Promise<GiftReservation> {
    try {
      const response = await EventParkAPI().post<GiftReservation>(
        `/v1/guest/gifts/cash/${giftId}/reserve`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to reserve cash gift",
      };
    }
  },

  /**
   * Create reservation for an item gift
   */
  async reserveItemGift(giftId: string): Promise<GiftReservation> {
    try {
      const response = await EventParkAPI().post<GiftReservation>(
        `/v1/guest/gifts/item/${giftId}/reserve`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to reserve item gift",
      };
    }
  },

  /**
   * Fetch reservations for a gifter
   */
  async getReservations(
    params: ReservationQueryParams = {}
  ): Promise<ReservationsResponse> {
    try {
      const response = await EventParkAPI().get<ReservationsResponse>(
        `/v1/guest/reservations`,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch reservations",
      };
    }
  },

  /**
   * Cancel a gifter's reservation
   */
  async cancelReservation(reservationId: string): Promise<GiftReservation> {
    try {
      const response = await EventParkAPI().post<GiftReservation>(
        `/v1/guest/reservations/${reservationId}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to cancel reservation",
      };
    }
  },

  /**
   * Set order for a gifter's item reservation
   */
  async setReservationOrder(
    reservationId: string,
    request: OrderRequest
  ): Promise<GiftReservation> {
    try {
      const response = await EventParkAPI().post<GiftReservation>(
        `/v1/guest/reservations/${reservationId}/order`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to set reservation order",
      };
    }
  },

  /**
   * Initiate gift reservation access
   */
  async initiateAccess(
    request: AccessInitiateRequest
  ): Promise<AccessInitiateResponse> {
    try {
      const response = await EventParkAPI().post<AccessInitiateResponse>(
        `/v1/guest/reservations/access/initiate`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to initiate access",
      };
    }
  },

  /**
   * Verify gift reservation access OTP
   */
  async verifyOTP(request: OTPVerifyRequest): Promise<OTPVerifyResponse> {
    try {
      const response = await EventParkAPI().post<OTPVerifyResponse>(
        `/v1/guest/reservations/access/otp/verify`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to verify OTP",
      };
    }
  },
};
