/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export interface CreatedEventData {
  id: string;
  title: string;
  category_id: string;
  category_name?: string;
  date_from: string;
  date_to: string;
  description: string;
  location_address: string;
  banner_image_id: string | null;
  status?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

export interface CompleteEventData {
  id: string;
  title: string;
  description: string;
  category_id: string;
  category_name?: string;
  event_status: string;
  visibility: string;
  date_from: string;
  date_to: string;
  created_at: string;
  updated_at: string;
  location_name: string;
  location_address: string;
  location_lat?: number;
  location_long?: number;
  delivery_address?: string;
  delivery_address_id?: string;
  delivery_address_name?: string;
  delivery_address_lat?: number;
  delivery_address_long?: number;
  banner_image_id?: string | null;
  banner_preview_key?: string;
  banner_preview_url?: string;
  images?: Array<{
    id: string;
    preview_key: string;
    preview_url: string;
  }>;
  iv_preview_key?: string;
  iv_preview_url?: string;
  iv_template_id?: string;
  iv_template_modifications?: any;
  iv_type?: string;
  gift_registry_status?: string;
  gift_registry_title?: string;
  most_wanted_gift_id?: string;
  gift_count?: {
    total_cash_gifts: number;
    total_item_gifts: number;
  };
  guestlist_status?: string;
  guest_count?: {
    total: number;
    pending: number;
    confirmed: number;
    declined: number;
  };
  reminders?: Array<{
    id: string;
    type: string;
    scheduled_at: string;
    status: string;
  }>;
  user_id: string;
}

interface EventsMetaData {
  from: number | null;
  to: number | null;
  page: number;
  per_page: number;
  previous_page: boolean;
  next_page: boolean;
  page_count: number;
  total: number;
}

type EventState = {
  createdEventData: CreatedEventData | null;
  userEvents: CreatedEventData[];
  selectedEvent: CreatedEventData | null;
  eventsMeta: EventsMetaData | null;
  setCreatedEventData: (eventData: CreatedEventData) => void;
  setUserEvents: (events: CreatedEventData[], meta?: EventsMetaData) => void;
  setSelectedEvent: (event: CreatedEventData | null) => void;
  clearCreatedEventData: () => void;
  clearAllEventData: () => void;
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      createdEventData: null,
      userEvents: [],
      selectedEvent: null,
      eventsMeta: null,
      setCreatedEventData: (eventData) => set({ createdEventData: eventData }),
      setUserEvents: (events, meta) => {
        const currentState = get();
        // Only set selectedEvent if there's already one selected, don't auto-select first event
        const newSelectedEvent = currentState.selectedEvent || null;

        set({
          userEvents: events,
          eventsMeta: meta || null,
          selectedEvent: newSelectedEvent,
        });
      },
      setSelectedEvent: (event) => set({ selectedEvent: event }),
      clearCreatedEventData: () => set({ createdEventData: null }),
      clearAllEventData: () =>
        set({
          createdEventData: null,
          userEvents: [],
          selectedEvent: null,
          eventsMeta: null,
        }),
    }),
    {
      name: 'event-store',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
