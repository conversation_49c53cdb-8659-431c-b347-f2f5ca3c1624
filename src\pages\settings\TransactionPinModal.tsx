import { ArrowCircleRight2 } from 'iconsax-react';
import { useState, useRef, useEffect } from 'react';

interface TransactionPinModalProps {
  onSave: () => void;
  // onClose: () => void;
}

export const TransactionPinModal: React.FC<TransactionPinModalProps> = ({
  onSave,
  // onClose,
}) => {
  const [pin, setPin] = useState(['', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleChange = (index: number, value: string) => {
    if (value.length > 1) return;
    if (!/^\d*$/.test(value)) return; // Only allow digits

    const newPin = [...pin];
    newPin[index] = value;
    setPin(newPin);

    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !pin[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 4);
    if (!/^\d+$/.test(pastedData)) return; // Only allow digits

    const newPin = [...pin];
    for (let i = 0; i < pastedData.length && i < 4; i++) {
      newPin[i] = pastedData[i];
    }
    setPin(newPin);
    const nextIndex = Math.min(pastedData.length, 3);
    inputRefs.current[nextIndex]?.focus();
  };

  const isPinComplete = pin.every((digit) => digit !== '');

  const handleContinue = () => {
    if (isPinComplete) {
      onSave();
    }
  };

  return (
    <div className="bg-white rounded-2xl p-4 mb-5 shadow-[0px_12px_120px_0px_#5F5F5F0F]">
      <div className="flex-1">
        <div className="tracking-[0.12em] text-primary-50 font-medium text-sm">
          TRANSACTION PIN
        </div>
        <h3 className="text-2xl font-semibold mt-3.5 mb-2">
          Set Transaction Pin
        </h3>
        <p className="text-base text-grey-960 mb-8">
          For safe and secure withdrawals
        </p>

        <div className="mb-8 border py-5 px-4 rounded-xl border-[#F0F0F0]">
          <p className="text-lg  mb-6 max-w-[382px]">
            Create your preferred 4 digit transaction pin to securely perform
            your transactions
          </p>

          <div className="flex gap-2 ">
            {pin.map((digit, index) => (
              <input
                key={index}
                ref={(el) => {
                  if (el) inputRefs.current[index] = el;
                }}
                type="text"
                inputMode="numeric"
                placeholder="0"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className={`w-[56px] h-[56px] placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[48px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                  digit
                    ? 'text-[#00000D] border-[#DBDDFC]'
                    : 'border-stroke-gray-300'
                } focus:outline-none focus:border-[#A6AAF9] focus:text-primary focus:shadow-[0px_0px_0px_4px_#DBDDFC]`}
              />
            ))}
          </div>
        </div>

        <button
          onClick={handleContinue}
          disabled={!isPinComplete}
          className={`flex items-center gap-1 px-4 py-3 rounded-full text-sm font-semibold cursor-pointer transition-colors ${
            isPinComplete
              ? 'bg-primary text-white hover:bg-primary-600'
              : 'bg-grey-200 text-grey-400 cursor-not-allowed'
          }`}>
          Continue 
          <ArrowCircleRight2 size={20} color="#fff" variant="Bulk" />
        </button>
      </div>
    </div>
  );
};
