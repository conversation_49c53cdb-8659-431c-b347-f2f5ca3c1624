import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect, useState } from 'react';
import { isTokenValid } from './helpers';
import { useUserAuthStore } from './store/auth';
import { useEventStore } from './store/event';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const { clearAllEventData } = useEventStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token || (token && !isTokenValid(token))) {
      if (token) {
        clearAuthData();
        clearAllEventData();
        console.error('Session expired, please login again');
      }
      navigate('/login');
    }
  }, [token, navigate, clearAuthData, clearAllEventData]);

  if (!token || (token && !isTokenValid(token))) {
    return null;
  }

  return children;
};

export const AuthRoute = () => {
  const { userEvents, selectedEvent, setSelectedEvent } = useEventStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  if (token) {
    // If user has exactly one event and no selected event, auto-select it and go to dashboard
    if (userEvents.length === 1 && !selectedEvent) {
      setSelectedEvent(userEvents[0]);
      return <Navigate to="/" replace />;
    }
    // If user has multiple events but no selected event, redirect to event selection
    if (userEvents.length > 1 && !selectedEvent) {
      return <Navigate to="/select-event" replace />;
    }
    // If user has events and a selected event, redirect to dashboard
    if (userEvents.length > 0 && selectedEvent) {
      return <Navigate to="/" replace />;
    }
    // If user has no events, they'll be redirected to onboarding by the dashboard
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};

export const PublicRoute = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};
export const OnboardingRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents } = useEventStore();
  const { clearAllEventData } = useEventStore();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!token || !isTokenValid(token)) {
        clearAuthData();
        clearAllEventData();
        navigate('/login');
        return;
      }
      // if (userEvents.length > 0) {
      //   navigate('/', { replace: true });
      //   return;
      // }

      setIsLoading(false);
    };

    checkUserStatus();
  }, [token, navigate, clearAuthData, clearAllEventData, userEvents]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!token || !isTokenValid(token)) {
    return null;
  }

  return children;
};
